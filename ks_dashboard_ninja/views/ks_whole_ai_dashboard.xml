<odoo>
    <data>

        <record id="ks_ai_whole_dashboard_form_view" model="ir.ui.view">
            <field name="name">ks_dashboard_ninja.ai.dashboard form</field>
            <field name="model">ks_dashboard_ninja.ai_dashboard</field>
            <field name="arch" type="xml">
                <form string="AI Dashboard" class="ks_import_dashboard_d_none">
                    <group>
                        <group>
                            <field name="ks_import_model_id" string="Model"/>
                            <field name='ks_dash_name'/>
                        </group>
                        <group>
                            <field name="ks_menu_name"/>
                            <field name="ks_top_menu_id"/>
                            <field name="ks_template" invisible="1"/>
                        </group>
                    </group>
                    <footer>
                        <button name="ks_do_action" string="Generate dashboard with AI" type="object"
                                class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


        <record id="ks_dashboard_ninja_ai_dashboardaction" model="ir.actions.act_window">
            <field name="name">AI whole Dashboard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ks_dashboard_ninja.ai_dashboard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ks_ai_whole_dashboard_form_view"/>
            <field name="target">new</field>
        </record>


    </data>

</odoo>
<odoo>
    <data>
        <!--Tree View Dashboard Items Tab 2-->
        <record model="ir.ui.view" id="ks_dashboard_ninja.item_tab2">
            <field name="name">Dashboard Items Tab 2</field>
            <field name="model">ks_dashboard_ninja.item_tab2</field>
            <field name="arch" type="xml">
                <tree create="false">
                    <field name="id" optional="show"/>
                    <field name="name" optional="show"/>
                    <field name="ks_dashboard_ninja_board_id" optional="show" string="Dashboard"/>
                    <field name="ks_dashboard_item_type" optional="show"/>
                    <field name="ks_model_id" optional="show"/>
                    <field name="ks_company_id" optional="show"/>
                    <field name="ks_date_filter_field" optional="show"/>
                    <field name="ks_date_filter_selection" optional="show"/>
                    <field name="ks_item_start_date" optional="show"/>
                    <field name="ks_item_end_date" optional="show"/>
                </tree>
            </field>
        </record>

        <!--Search View Of Dashboard Items Tab 2-->
        <record id="ks_item_tab2_search_view" model="ir.ui.view">
            <field name="name">dashboard.items.tab2.search.view</field>
            <field name="model">ks_dashboard_ninja.item_tab2</field>
            <field name="arch" type="xml">
                <search string="Search Items Tab 2">
                    <field name="name"
                           filter_domain="[('name','ilike',self)]"/>
                    <field name="ks_dashboard_ninja_board_id"/>
                    <field name="ks_model_id" filter_domain="[('ks_model_id.model','ilike',self)]"/>
                    <field name="ks_dashboard_item_type" filter_domain="[('ks_dashboard_item_type','ilike',self)]"/>
                </search>
            </field>
        </record>

        <record model="ir.ui.view" id="ks_dashboard_ninja.item_tab2_quick_edit_form_view">
            <field name="name">ks_dashboard_ninja_item_tab2 form</field>
            <field name="model">ks_dashboard_ninja.item_tab2</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <form create="false" delete="false" class="ks_qe_form_view">
                    <group class="ks_qe_form_view_group">
                        <field name="ks_chart_data" invisible="1"/>
                        <field name="ks_domain_temp" invisible="1"/>
                        <field name="ks_font_color" invisible="1"/>
                        <field name="ks_date_filter_field" invisible="1"/>
                        <field name="ks_background_color" invisible="1"/>
                        <field name="ks_icon" invisible="1"/>
                        <field name="ks_icon_select" invisible="1"/>
                        <field name="ks_model_name" invisible="1"/>
                        <field name="ks_date_filter_selection" invisible="1"/>
                        <field name="ks_model_name_2" invisible="1"/>
                        <field name="ks_model_id_2" invisible="1"/>
                        <field name="ks_item_start_date" invisible="1"/>
                        <field name="ks_item_end_date" invisible="1"/>
                        <field name="ks_record_field_2" invisible="1"/>
                        <field name="ks_record_count_type_2" invisible="1"/>
                        <field name="name" placeholder="Name..."/>
                        <field name="ks_model_id" placeholder="Model..."
                               options="{'no_create': True, 'no_create_edit':True, 'no_open': True, 'limit': 10}"
                               invisible="ks_dashboard_item_type not in
                                                        ['ks_kpi','ks_tile','ks_bar_chart','ks_horizontalBar_chart','ks_line_chart','ks_area_chart','ks_pie_chart','ks_map_view','ks_funnel_chart'
                                                        'ks_doughnut_chart','ks_polarArea_chart','ks_list_view','ks_radar_view','ks_flower_view','ks_scatter_chart','ks_radialBar_chart']"/>
                        <field name="ks_dashboard_item_type" invisible="1"/>
                    </group>

                    <group invisible="ks_dashboard_item_type != 'ks_tile' or ks_dashboard_item_type != 'ks_kpi'"
                           class="ks_qe_form_view_group">
                        <field name="ks_record_count_type"
                               invisible="ks_model_id == False"
                               required="ks_model_id != False and ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_kpi'"/>
                        <field name="ks_record_field" placeholder="Record Field..."
                               options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                               invisible="ks_record_count_type == 'count'"
                               required="ks_record_count_type != 'count' and ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_kpi'"/>
                        <field name="ks_record_count" placeholder="Count..."
                               string="Record Value"/>
                        <field name="ks_layout" placeholder="Layout..."
                               invisible="ks_dashboard_item_type != 'ks_tile'"/>
                        <field name="ks_dashboard_item_theme" widget="ks_dashboard_item_theme"/>
                    </group>

                    <group name="chart_settings"
                           invisible="ks_dashboard_item_type in ['ks_tile','ks_kpi']"
                           class="ks_qe_form_view_group">
                        <field name="ks_chart_data_count_type"
                               invisible="ks_model_id == False or ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_list_view'"
                               required="ks_dashboard_item_type != 'ks_tile' or ks_dashboard_item_type != 'ks_list_view'"/>

                        <field name="ks_chart_measure_field" string="Measures" widget='many2many_tags'
                               options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                               invisible="ks_chart_data_count_type == 'count' or ks_model_id == False or ks_dashboard_item_type == 'ks_list_view' or ks_dashboard_item_type == 'ks_funnel_chart'"/>

                        <field name="ks_chart_groupby_type" invisible="1"/>
                        <field name="ks_chart_relation_groupby" string="Group By"
                               options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                               invisible="ks_dashboard_item_type == 'ks_scatter_chart' or ks_model_id == False or ks_dashboard_item_type == 'ks_list_view'"/>
                        <field name="ks_chart_date_groupby" string="Group By Date"
                               invisible="ks_chart_groupby_type != 'date_type'"
                               required="ks_dashboard_item_type != 'ks_tile' or ks_chart_groupby_type == 'date_type'"/>
                    </group>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="ks_dashboard_ninja.item_tab2_form_view">
            <field name="name">ks_dashboard_ninja_item_tab2 form</field>
            <field name="model">ks_dashboard_ninja.item_tab2</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <form create="false" delete="false" class="ks_dashboard_ninja ks_create_chart_body">
                    <group>
                        <div class="left-70 ks-char-preview">
                            <div class="ks-chart-inner">
                                <div class="ks-modal-title">
                                    <span>Preview</span>
                                </div>
                                <field name="ks_preview" widget="ks_dashboard_item_preview_owl"
                                       invisible="ks_dashboard_item_type != 'ks_tile'"
                                />
                                <field name="ks_graph_preview" string="Preview"
                                       widget="ks_dashboard_graph_preview"
                                       invisible="ks_dashboard_item_type in
                                   ['ks_to_do', 'ks_tile', 'ks_list_view', 'ks_kpi', 'ks_map_view', 'ks_funnel_chart', 'ks_flower_view', 'ks_bullet_chart', 'ks_radialBar_chart', 'ks_kpi', 'ks_radialBar_chart']"/>
                                <notebook>
                                    <page string="Data" name="data_sets">
                                        <group
                                                invisible="ks_dashboard_item_type != 'ks_tile' and ks_dashboard_item_type != 'ks_kpi'">
                                            <field name="ks_record_count_type"
                                                   invisible="ks_model_id == False"
                                                   required="ks_model_id != False and ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_kpi'"/>

                                            <field name="ks_record_field"
                                                   options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                                   invisible="ks_record_count_type == 'count' or  (ks_dashboard_item_type != 'ks_kpi' and ks_dashboard_item_type != 'ks_tile')"/>
                                            <field name="ks_record_count" string="Record Value"/>
                                        </group>
                                        <group invisible="ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_kpi'">
                                            <field name="ks_chart_measure_field" string="Measures"
                                                   widget='many2many_tags'
                                                   options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                                   context="{'current_id': id}"
                                                   invisible="ks_chart_data_count_type == 'count'  or ks_model_id == False or ks_dashboard_item_type == 'ks_list_view' or ks_dashboard_item_type == 'ks_map_view' or ks_dashboard_item_type == 'ks_funnel_chart'"/>
                                        </group>
                                        <group>
                                            <field name="ks_chart_data_count_type" context="{'current_id': id}"
                                                   invisible="ks_model_id == False or ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_list_view' or ks_dashboard_item_type == 'ks_kpi' or ks_dashboard_item_type == 'ks_map_view'"
                                                   required="ks_dashboard_item_type != 'ks_kpi' or ks_dashboard_item_type != 'ks_tile' or ks_dashboard_item_type != 'ks_list_view' or ks_dashboard_item_type != 'ks_scatter_chart'"/>
                                        </group>
                                        <group string="Groups/Dimensions" name="ks_groups_dimensions"
                                               invisible="ks_dashboard_item_type == 'ks_tile' or ks_dashboard_item_type == 'ks_to_do' or ks_dashboard_item_type == 'ks_kpi'">
                                            <field name="ks_chart_groupby_type" invisible="1"/>
                                            <field name="ks_chart_sub_groupby_type" invisible="1"/>
                                            <field name="ks_chart_relation_groupby" string="Group By"
                                                   context="{'current_id': id}"
                                                   options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                                   invisible="ks_dashboard_item_type == 'ks_scatter_chart' or ks_dashboard_item_type == 'ks_map_view' or ks_model_id == False"/>
                                            <field name="ks_chart_date_groupby" string="Group By Date"
                                                   context="{'current_id': id}"
                                                   invisible="ks_chart_groupby_type !='date_type'"
                                                   required="(ks_chart_groupby_type == 'date_type') and (ks_dashboard_item_type != 'ks_kpi') and (ks_dashboard_item_type != 'ks_tile' and ks_dashboard_item_type != 'ks_list_view')"/>
                                            <field name="ks_chart_relation_sub_groupby" invisible="1"/>
                                        </group>
                                        <group string="Filter"
                                               invisible="ks_dashboard_item_type == 'ks_to_do'">
                                            <field name="ks_domain" widget="domain" class="ks_domain_content"
                                                   context="{'current_id': id}"
                                                   options="{'model': 'ks_model_name', 'in_dialog': True}"/>
                                            <field name="ks_date_filter_field" context="{'current_id': id}"
                                                   options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                                   invisible="ks_model_id == False"/>
                                            <field name="ks_date_filter_selection"
                                                   invisible="(ks_model_id == False) or (ks_date_filter_field == False)"/>
                                            <field name="ks_item_start_date" string="Start Date"
                                                   invisible="(ks_model_id == False) or (ks_date_filter_selection != 'l_custom')"
                                                   required="(ks_model_id != False) and (ks_date_filter_selection == 'l_custom')"/>
                                            <field name="ks_item_end_date"
                                                   invisible="(ks_model_id == False) or (ks_date_filter_selection != 'l_custom')"
                                                   required="(ks_model_id != False) and (ks_date_filter_selection == 'l_custom')"/>
                                        </group>
                                    </page>
                                    <page string="Display" name="display_settings">
                                        <group name="ks_layouts"
                                               invisible="(ks_dashboard_item_type != 'ks_tile') and (ks_dashboard_item_type != 'ks_kpi')">
                                            <field name="ks_layout"
                                                   invisible="(ks_dashboard_item_type != 'ks_tile') and (ks_dashboard_item_type != 'ks_list_view')"/>
                                            <field name="ks_dashboard_item_theme" widget="ks_dashboard_item_theme"/>
                                            <field name="ks_background_color" widget="Ks_dashboard_color_picker_owl"/>
                                            <field name="ks_font_color" widget="Ks_dashboard_color_picker_owl"/>
                                            <field name="ks_icon_select" widget="radio"/>
                                            <field name="ks_icon" string="Icon" widget="image" class="ks_item_icon"
                                                   invisible="(ks_icon_select == 'Default')"/>
                                            <field name="ks_default_icon" widget="ks_image_widget" class="ks_item_icon"
                                                   invisible="(ks_icon_select == 'Custom')"/>
                                            <field name="ks_default_icon_color" widget="Ks_dashboard_color_picker_owl"
                                                   invisible="(ks_icon_select == 'Custom') or (ks_default_icon == False)"/>
                                        </group>
                                    </page>
                                </notebook>
                            </div>
                        </div>
                        <div class="right-30 ks-chart-form">
                            <div class="ks-chart-form-inner">
                                <div class="ks-modal-title">
                                    <span>Configuration</span>
                                </div>
                                <group>
                                    <field name="name" required="1"/>
                                    <field name="ks_dashboard_item_type" widget="radio" required="1"/>
                                    <field name="ks_model_id"
                                           options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                           invisible="ks_dashboard_item_type == 'ks_to_do'"
                                           required="ks_dashboard_item_type != 'ks_to_do'"/>
                                    <field name="ks_model_name" invisible="1"/>
                                </group>
                            </div>
                        </div>
                    </group>
                </form>
            </field>
        </record>

        <!-- Action for Dashboard Items Tab 2 -->
        <record id="ks_dashboard_ninja_item_tab2_action" model="ir.actions.act_window">
            <field name="name">Dashboard Items Tab 2</field>
            <field name="res_model">ks_dashboard_ninja.item_tab2</field>
            <field name="view_mode">tree,form</field>
            <field name="view_type">form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Dashboard Item for Tab 2!
                </p>
            </field>
        </record>

    </data>
</odoo>

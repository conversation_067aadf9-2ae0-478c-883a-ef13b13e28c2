<odoo>
    <data>

        <record id="ks_dashboard_ninja_ai_form_view" model="ir.ui.view">
            <field name="name">ks_dashboard_ninja.arti_int form</field>
            <field name="model">ks_dashboard_ninja.arti_int</field>
            <field name="arch" type="xml">
                <form string="AI Dashboard" class="ks_import_dashboard_d_none ks_dashboard_ninja ks_create_chart_body">
                    <div class="ks-modal-body-inner">
                        <div class="ks-modal-title">
                            <span>Generate items with AI</span>
                        </div>
                        <div class="ks-generateAI-body">
                            <group>
                                <group>
                                    <field name="ks_type" nolabel="1" string="Generate with" required="1"/>
                                </group>
                                <group>
                                    <field name="ks_import_model_id"
                                           string="Model" invisible="(ks_type == 'ks_keyword')"
                                           required="(ks_type == 'ks_model')" placeholder="Enter Model" nolabel="1"/>
                                    <field class="ks_import_model_class" name="ks_import_model" string="Model" nolabel="1"
                                           invisible="(ks_model_show == False)"
                                           required="(ks_model_show == True)"/>
                                </group>
                                <group>
                                    <field name="ks_input_keywords"
                                           string="Keywords"
                                           nolabel="1"
                                           widget="ks_keyword_selection"
                                           invisible="(ks_type == 'ks_model')"
                                           required="(ks_type == 'ks_keyword')"/>
                                    <field name="ks_model_show"
                                           invisible="(ks_type == 'ks_keyword') or (ks_type == 'ks_model')"/>

                                </group>

                            </group>
                        </div>
                    </div>
                    <footer>
                        <button name="ks_do_action"
                                string="Generate with AI"
                                type="object"
                                invisible="(ks_type == 'ks_keyword')"
                                class="btn-primary"/>
                        <button name="ks_generate_item"
                                string="Generate Item"
                                invisible="(ks_type == 'ks_model')"
                                type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


        <record id="ks_dashboard_ninja_ai_action" model="ir.actions.act_window">
            <field name="name">AI Dashboard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ks_dashboard_ninja.arti_int</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ks_dashboard_ninja_ai_form_view"/>
            <field name="target">new</field>
        </record>


    </data>

</odoo>
<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="res_config_settings_inherit_view" model="ir.ui.view">
        <field name="name">res.config.settings.inherit.view</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id"
               ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='invite_users']" position="after">
                <div id="dashboard_api_key">
                    <h2>Dashboard API Key</h2>
                    <div class="row mt16 o_settings_container" name="dn_ai_api_key_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="dn_ai_api_key">
                            <div class="o_setting_right_pane">
                                <label for="dn_api_key" class="col-sm-5 col-form-label">
                                    Dashboard Ninja Api Key
                                </label>
                                <field name="dn_api_key"/>
                                <label for="ks_email_id" class="col-sm-5 col-form-label">
                                    Email ID
                                </label>
                                <field name="ks_email_id"/>
                                <div class="o_row">
                                    <label for="url" class="col-sm-5 col-form-label">
                                        URL
                                    </label>
                                    <field name="url"/>
                                    <button name="Open_wizard" string="Get API key" type="object"
                                            class="btn-primary"/>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>

<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_send_target_email" model="ir.cron">
        <field name="name">Kpi mail cron</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="model_id" ref="model_ks_dashboard_ninja_item"/>
        <field name="code">model.check_target()</field>
        <field name="state">code</field>
    </record>
</odoo>
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="Ks_chart_list_container">
        <t t-if="item.ks_dashboard_item_type == 'ks_list_view'">
            <t t-call="ks_dashboard_ninja.Ksdashboardlistview"/>
        </t>
        <t t-else="">
            <t t-call="Ks_gridstack_container"/>
        </t>
    </t>

    <t t-name="Ks_gridstack_container">
        <div class="grid-stack-item ks_chart_container ks_dashboarditem_id" t-att-id="chart_id" t-ref="ks_gridstack_container">
            <div class="grid-stack-item-content ks_dashboarditem_chart_container ks_dashboard_item_hover card shadow"
                 t-att-title="ks_info">

                <div class="p-3 py-3 d-flex flex-row align-items-center justify-content-between ">
                    <div class="d-flex align-items-center w-50">
                        <h6 class="m-0 font-weight-bold h3 ks_chart_heading mr-3" t-att-title="ks_chart_title">
                            <t t-esc="ks_chart_title"/>
                        </h6>
                        <!--                    <div >-->
                            <t t-if="ks_breadcrumb.length >= 1">
                                <nav class="ks_breadcrumb">
                                <ul>
                                    <li class="d-none" t-att-id="'item'+'_'+'-1'">
                                           <span t-att-data-sequence = '-1' t-att-data-item-id="chart_id" t-on-click="ksOnDrillUp">
                                               <t t-esc="ks_chart_title"/>
                                           </span>
                                    </li>
                                    <t t-foreach="ks_breadcrumb" t-as="chart_breadcrumb" t-key="chart_breadcrumb_index">
                                            <li class="d-none" t-att-id="chart_breadcrumb['name'] + '_' + chart_breadcrumb_index">
                                            <span t-att-data-sequence="chart_breadcrumb_index" t-att-data-item-id="chart_id" t-on-click="ksOnDrillUp">
                                               <t t-esc="chart_breadcrumb['name']"/>
                                            </span>
                                        </li>
                                    </t>
                                </ul>
                                </nav>
                            </t>
                    </div>
                    <!--                    </div>-->
                    <img src="ks_dashboard_ninja/static/description/images/selected.svg" class="ks_img_display d-none"
                         width="30"/>
                    <!--  Dashboard Item Move/Copy Feature -->
                    <div class="ks_dashboard_item_button_container ks_dropdown_container ks_dashboard_item_header ks_dashboard_item_header_hover chart_button_container d-md-flex d-none"
                         t-att-data-item_id="chart_id">

                        <t t-if="ksIsDashboardManager">
                                <button class="ks_dashboard_item_chart_info" title="More Info" type="button"
                                        t-att-data-item-id="chart_id">
                                    <i class="fa fa-info-circle"/>
                                </button>

                            <t t-if="ks_dashboard_item_type != 'ks_map_view'">
                                <div class="ks_chart_inner_buttons">
                                    <button title="Color Palette" data-bs-toggle="dropdown"
                                            class="ks_dashboard_item_action ks_dashboard_color_option btn dropdown-toggle btn-xs o-no-caret btn"
                                            type="button"
                                            aria-expanded="true">
                                        <i class="fa fa-paint-brush"/>
                                    </button>
                                    <ul role="menu" class="dropdown-menu dropdown-menu-right ks_color_pallate"
                                        t-att-data-item-id="chart_id"
                                        t-att-data-chart-type="chart_type" t-att-data-chart-family="chart_family">
                                        <t t-foreach="ksChartColorOptions" t-as="color_option"
                                           t-key="color_option_index">
                                            <li t-att-class="'ks_li_'+color_option">
                                                <span t-att-class="color_option + ' ks_chart_color_options'"
                                                      t-att-data-chart-color="color_option" t-on-click="ksRenderChartColorOptions">
                                                    <t t-esc="color_option"/>
                                                </span>
                                            </li>
                                        </t>
                                    </ul>
                                </div>
                            </t>


                            <div class="ks_chart_inner_buttons">
                                <button title="Move/Duplicate" data-bs-toggle="dropdown"
                                        class="ks_dashboard_item_action btn dropdown-toggle btn-xs" type="button"
                                        aria-expanded="true">
                                    <i class="fa fa-files-o"/>
                                </button>
                                <ul role="menu" class="ks_dashboard_menu_container dropdown-menu dropdown-menu-right">
                                    <li class="ks_md_heading">
                                        <span>Select Dashboard</span>
                                    </li>
                                    <li class="my-3">
                                        <select class="o_input o_group_selector o_add_group ks_dashboard_select">
                                            <t t-foreach="ks_dashboard_list" t-as="ks_dashboard"
                                               t-key="ks_dashboard_index">
                                                <option t-att-value="ks_dashboard['id']">
                                                    <t t-esc="ks_dashboard['name']"/>
                                                </option>
                                            </t>
                                        </select>
                                    </li>
                                    <li class="mt-3">
                                        <button class="btn btn-primary o_apply_group o_add_group ks_duplicate_item"
                                                tabindex="-1" type="button">Duplicate
                                        </button>
                                        <button class="btn btn-primary o_apply_group o_add_group ks_move_item"
                                                tabindex="-1"
                                                type="button">Move
                                        </button>
                                    </li>
                                </ul>
                            </div>

                            <button title="Quick Customize"
                                    class="ks_dashboard_quick_edit_action_popup  btn d-sm-block d-none"
                                    type="button" t-att-data-item-id="chart_id">
                                <i class="fa fa-pencil"/>
                            </button>

                            <button class="ks_dashboard_item_customize d-block d-sm-none" title="Customize Item"
                                    type="button">
                                <i class="fa fa-pencil"/>
                            </button>

                            <button class="ks_dashboard_item_delete" title="Remove Item" type="button">
                                <i class="fa fa-times"/>
                            </button>
                        </t>
                        <t t-if="ksIsUser">
                                <div class="ks_chart_inner_buttons ks_dashboard_more_action"
                                     t-att-data-item-id="chart_id">
                                    <button title="Export Chart" data-bs-toggle="dropdown"
                                            class="ks_dashboard_item_action  btn dropdown-toggle btn-xs o-no-caret btn"
                                            type="button"
                                            aria-expanded="true">
                                        <i class="fa fa-ellipsis-v"/>
                                    </button>
                                    <div role="menu" class="dropdown-menu dropdown-menu-right">
                                        <!--Dynamic Rendering-->
                                        <t t-call="ksMoreChartOptions"/>
                                    </div>
                                </div>

                        </t>
                        <div class="ks_chart_inner_buttons dropdown">
                        <button title="Info" data-bs-toggle="dropdown"
                                class="ks_item_description btn dropdown-toggle btn-xs o-no-caret btn"
                                type="button"
                                t-att-style="'color:'+ ks_rgba_button_color + ';'"
                                aria-expanded="true">
                            <i class="fa fa-book"/>
                        </button>
                            <div role="menu" class="dropdown-menu dropdown-menu-right" style="width:20rem">
                            <!--Dynamic Rendering-->
                                <div class="ks_chart_export_menu">
                                <div class="ks_chart_export_menu_header" style="margin-left:-10px">
                                    <span>Info</span>
                                </div>
                                    <div class="ks_info" style="margin-left:10px">
                                    <span>Company: <t t-esc="ks_company"/></span>
                                </div>
                                    <div class="ks_info" style="margin-left:10px">
                                    <t t-if="ks_info">
                                        <t t-foreach="ks_info" t-as="ks_description" t-key="ks_description_index">
                                            <span><t t-esc="ks_description"/></span>
                                            <br></br>
                                         </t>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                   </div>
                    <t t-if="ksIsDashboardManager">
                        <div class="dropdown d-md-none dn-setting-panel">
                    <button class="btn btn-secondary border-0" type="button" id="dropdownSettingButton"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                     <i class="fa fa-cog"></i>
                 </button>

                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownSettingButton">
                        <t t-if="ks_show_records &amp; ks_query_type=='custom'">
                            <a class="dropdown-item ks_dashboard_item_chart_info ks_info_display"
                               t-att-data-item-id="chart_id">
                             <i class="fa fa-info-circle"/>
                                More Info
                        </a>
                        </t>
                                <a class="dropdown-item ks_dashboard_item_customize">
                            <i class="fa fa-pencil"></i>
                                    Customize Item
                         </a>

                                <a class="dropdown-item ks_dashboard_item_delete">
                            <i class="fa fa-times"></i>
                                    Remove Item
                         </a>
                </div>
                </div>
                    </t>
                </div>
                <div class="card-body ks_chart_card_body"/>
            </div>
        </div>
    </t>

    <t t-name="ksMoreChartOptions">
        <div class="ks_chart_export_menu">
            <div class="ks_chart_export_menu_header">
                <span>Export</span>
            </div>
            <div class="ks_chart_xls_csv_export ks_chart_export_menu_item" t-att-data-chart-id="chart_id"
                 data-format="chart_xls">
                <i class="fa fa-file-excel-o"/>
                <span>Export to Excel</span>
            </div>
            <div class="ks_chart_xls_csv_export ks_chart_export_menu_item" t-att-data-chart-id="chart_id"
                 data-format="chart_csv">
                <i class="fa fa-file-code-o"/>
                <span>Export to CSV</span>
            </div>

            <div t-att-data-chart-id="chart_id" class="ks_chart_pdf_export ks_chart_export_menu_item">
                <i class="fa fa-file-pdf-o"/>
                <span>Save as PDF</span>
            </div>
            <a t-att-data-chart-id="chart_id" class="ks_chart_export_menu_item ks_chart_image_export">
                <i class="fa fa-file-image-o"/>
                <span>Save as Image</span>
            </a>
            <div class="ks_chart_json_export ks_chart_export_menu_item" t-att-data-item-id="item_id"
                 data-format="chart_xls">
                <i class="fa fa-file-code-o"/>
                <span>Export Item</span>
            </div>
        </div>
    </t>
</templates>
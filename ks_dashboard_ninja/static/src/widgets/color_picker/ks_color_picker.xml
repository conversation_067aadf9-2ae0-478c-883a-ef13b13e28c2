<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="Ks_color_picker_opacity_view" owl="1">
        <input class="ks_color_picker" type="color" t-att-value="value['ks_color']"
               t-on-change="(ev) => this._ksOnColorChange(ev)"/>
        Transparency :
        <input type="range" t-att-value="value['ks_opacity']" class="ks_color_opacity" name="ks_db_item_opacity"
               min="0" max="0.99" step="0.01" t-on-change="(ev) => this. _ksOnOpacityChange(ev)"/>
<!--               t-on-input="(ev) => this._ksOnOpacityInput(ev)"/>-->
    </t>
</templates>
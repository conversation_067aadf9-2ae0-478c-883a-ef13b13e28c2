
.ks_icon_container_grid_view{
    width:100%;
   /* height:100%;*/
}

.ks_icon_container_list{
   border: 1px solid transparent;
    position: relative;
    padding:2%;
    float:left;
    display:block;
    transition: 0.3s ease-in-out;
    width: 16.66%;
    text-align: center;
 }

 .ks_font {
    font-weight: bold;
    min-width: 129px
 }


.ks_icon_container_list:hover {
    transform: scale(1.2);
    cursor: pointer;
}

.ks_icon_selected{
    background: #f2f2f2;
    border-radius: 5px;
    border: 1px solid black;
}
.ks_icon_selected img{
    transform: scale(0.7) !important;
    padding: 5px;
}



.ks_modal_icon_input_div{
    width: 100%;
    text-align: right;
    margin-bottom: 12px;
}

.ks_modal_icon_input{
    padding: 6px;
    margin-top: 8px;
    font-size: 17px;
}

.ks_fa_icon_search{
    float: right;
    padding: 6px 10px;
    margin-top: 8px;
    margin-right: 16px;
    background: #ddd;
    font-size: 17px;
    border: none;
    cursor: pointer;
}

.ks_search_modal_container{
    display: flex;
    justify-content: flex-end;
}

.x-tree-icon-leaf, .x-tree-icon-text::before {
  font: normal normal normal 14px/1 "FontAwesome";
  display: inline-block;
  color: #5fa2dd;
}
@media (max-width: 475px){
.ks_icon_container_list{
    width: 33.33%;
}
}


/* Responsive CSS */
@media (max-width: 1024px){
    .ks_dashboard_item_header_hover > button, .ks_dashboard_item_header_hover > .ks_chart_inner_buttons{
        visibility: visible !important;
    }
}

@media (max-width: 1024px) {
     .dropdown-max-height {
        max-height: 40vh;
        overflow: auto;
    }
}

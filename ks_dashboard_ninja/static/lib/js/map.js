"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[6842],{6970:function(t,e,n){n.r(e),n.d(e,{DefaultTheme:function(){return le},GraticuleSeries:function(){return rt},MapChart:function(){return Ei},MapLine:function(){return q},MapLineSeries:function(){return et},MapPointSeries:function(){return Ii},MapPolygon:function(){return Li},MapPolygonSeries:function(){return ki},MapSeries:function(){return a},ZoomControl:function(){return Yi},geoAlbersUsa:function(){return Vi},geoEqualEarth:function(){return Ki},geoEquirectangular:function(){return Zi},geoMercator:function(){return ae},geoNaturalEarth1:function(){return eo},geoOrthographic:function(){return Xi},getGeoArea:function(){return Di},getGeoBounds:function(){return Si},getGeoCentroid:function(){return xi},getGeoCircle:function(){return Pi},getGeoRectangle:function(){return Mi},normalizeGeoPoint:function(){return Ni}});var i=n(3399),o=n(5071),r=n(256);class a extends i.F{constructor(){super(...arguments),Object.defineProperty(this,"_types",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_geometries",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_geoJSONparsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"_excluded",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_notIncluded",{enumerable:!0,configurable:!0,writable:!0,value:[]})}_afterNew(){this.fields.push("geometry","geometryType"),this._setRawDefault("geometryField","geometry"),this._setRawDefault("geometryTypeField","geometryType"),this._setRawDefault("idField","id"),this.on("geoJSON",(t=>{let e=this._prevSettings.geoJSON;e&&e!=t&&this.data.clear()})),super._afterNew()}_handleDirties(){const t=this.get("geoJSON");let e=this._prevSettings.geoJSON;e&&e!=t&&(this._prevSettings.geoJSON=void 0,this._geoJSONparsed=!1),this._geoJSONparsed||(this._parseGeoJSON(),this._geoJSONparsed=!0)}_prepareChildren(){if(super._prepareChildren(),this._valuesDirty&&this._handleDirties(),this.isDirty("geoJSON")||this.isDirty("include")||this.isDirty("exclude")){this._handleDirties();const t=this.chart,e=this.get("exclude");e&&(t&&(t._centerLocation=null),o.each(e,(t=>{const e=this.getDataItemById(t);e&&this._excludeDataItem(e)}))),e&&0!=e.length||(o.each(this._excluded,(t=>{this._unexcludeDataItem(t)})),this._excluded=[]);const n=this.get("include");n&&(t&&(t._centerLocation=null),o.each(this.dataItems,(t=>{const e=t.get("id");e&&-1==n.indexOf(e)?this._notIncludeDataItem(t):this._unNotIncludeDataItem(t)}))),n||(o.each(this._notIncluded,(t=>{this._unNotIncludeDataItem(t)})),this._notIncluded=[])}}_excludeDataItem(t){this._removeGeometry(t.get("geometry")),o.move(this._excluded,t)}_unexcludeDataItem(t){this._addGeometry(t.get("geometry"),this)}_notIncludeDataItem(t){this._removeGeometry(t.get("geometry")),o.move(this._notIncluded,t)}_unNotIncludeDataItem(t){this._addGeometry(t.get("geometry"),this)}checkInclude(t,e,n){if(e){if(0==e.length)return!1;if(-1==e.indexOf(t))return!1}return!(n&&n.length>0&&-1!=n.indexOf(t))}_parseGeoJSON(){const t=this.get("geoJSON");if(t){let e;"FeatureCollection"==t.type?e=t.features:"Feature"==t.type?e=[t]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(t.type)?e=[{geometry:t}]:console.log("nothing found in geoJSON");const n=this.get("geodataNames");if(e){const t=this.get("idField","id");for(let i=0,a=e.length;i<a;i++){let a=e[i],s=a.geometry;if(s){let e=s.type,i=a[t];if(n&&n[i]&&(a.properties.name=n[i]),-1!=this._types.indexOf(e)){let n,l;null!=i&&(n=o.find(this.dataItems,(t=>t.get("id")==i))),n&&(l=n.dataContext),n?l.geometry||(l.geometry=s,l.geometryType=e,n.set("geometry",s),n.set("geometryType",e),this.processDataItem(n)):(l={geometry:s,geometryType:e,madeFromGeoData:!0},l[t]=i,this.data.push(l)),r.softCopyProperties(a.properties,l)}}}}const i="geodataprocessed";this.events.isEnabled(i)&&this.events.dispatch(i,{type:i,target:this})}}_placeBulletsContainer(t){this.children.moveValue(this.bulletsContainer)}_removeBulletsContainer(){}projection(){const t=this.chart;if(t)return t.get("projection")}geoPath(){const t=this.chart;if(t)return t.getPrivate("geoPath")}_addGeometry(t,e){if(t&&e.get("affectsBounds",!0)){this._geometries.push(t);const e=this.chart;e&&e.markDirtyGeometries()}}_removeGeometry(t){if(t){o.remove(this._geometries,t);const e=this.chart;e&&e.markDirtyGeometries()}}_dispose(){super._dispose();const t=this.chart;t&&t.series.removeValue(this)}_onDataClear(){super._onDataClear(),this._geoJSONparsed=!1,this._markDirtyKey("exclude")}}Object.defineProperty(a,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapSeries"}),Object.defineProperty(a,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:i.F.classNames.concat([a.className])});var s=n(5040),l=n(6245),u=n(1479);class c{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const e=this._partials;let n=0;for(let i=0;i<this._n&&i<32;i++){const o=e[i],r=t+o,a=Math.abs(t)<Math.abs(o)?t-(r-o):o-(r-t);a&&(e[n++]=a),t=r}return e[n]=t,this._n=n+1,this}valueOf(){const t=this._partials;let e,n,i,o=this._n,r=0;if(o>0){for(r=t[--o];o>0&&(e=r,n=t[--o],r=e+n,i=n-(r-e),!i););o>0&&(i<0&&t[o-1]<0||i>0&&t[o-1]>0)&&(n=2*i,e=r+n,n==e-r&&(r=e))}return r}}var h=1e-6,p=1e-12,f=Math.PI,g=f/2,d=f/4,m=2*f,y=180/f,v=f/180,_=Math.abs,b=Math.atan,w=Math.atan2,P=Math.cos,x=Math.ceil,D=Math.exp,S=(Math.floor,Math.hypot),M=Math.log,N=(Math.pow,Math.sin),j=Math.sign||function(t){return t>0?1:t<0?-1:0},C=Math.sqrt,E=Math.tan;function I(t){return t>1?g:t<-1?-g:Math.asin(t)}function O(t){return(t=N(t/2))*t}function T(){}function L(t,e){t&&B.hasOwnProperty(t.type)&&B[t.type](t,e)}var k,z,R,G,Y={Feature:function(t,e){L(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,i=-1,o=n.length;++i<o;)L(n[i].geometry,e)}},B={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,i=-1,o=n.length;++i<o;)t=n[i],e.point(t[0],t[1],t[2])},LineString:function(t,e){X(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,i=-1,o=n.length;++i<o;)X(n[i],e,0)},Polygon:function(t,e){A(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,i=-1,o=n.length;++i<o;)A(n[i],e)},GeometryCollection:function(t,e){for(var n=t.geometries,i=-1,o=n.length;++i<o;)L(n[i],e)}};function X(t,e,n){var i,o=-1,r=t.length-n;for(e.lineStart();++o<r;)i=t[o],e.point(i[0],i[1],i[2]);e.lineEnd()}function A(t,e){var n=-1,i=t.length;for(e.polygonStart();++n<i;)X(t[n],e,1);e.polygonEnd()}function Z(t,e){t&&Y.hasOwnProperty(t.type)?Y[t.type](t,e):L(t,e)}var W={sphere:T,point:T,lineStart:function(){W.point=V,W.lineEnd=F},lineEnd:T,polygonStart:T,polygonEnd:T};function F(){W.point=W.lineEnd=T}function V(t,e){z=t*=v,R=N(e*=v),G=P(e),W.point=J}function J(t,e){t*=v;var n=N(e*=v),i=P(e),o=_(t-z),r=P(o),a=i*N(o),s=G*n-R*i*r,l=R*n+G*i*r;k.add(w(C(a*a+s*s),l)),z=t,R=n,G=i}function $(t){return k=new c,Z(t,W),+k}var H=[null,null],Q={type:"LineString",coordinates:H};function U(t,e){return H[0]=t,H[1]=e,$(Q)}class q extends u.T{constructor(){super(...arguments),Object.defineProperty(this,"_projectionDirty",{enumerable:!0,configurable:!0,writable:!0,value:!1})}_beforeChanged(){if(super._beforeChanged(),this._projectionDirty||this.isDirty("geometry")||this.isDirty("precision")){const t=this.get("geometry");if(t){const e=this.getPrivate("series");if(e){const n=e.chart;if(n){const i=n.get("projection");let o=null;i&&i.clipAngle&&(o=i.clipAngle(),i.precision(this.get("precision",.5)));const r=this.dataItem,a=n.getPrivate("geoPath");if(a&&r)if(this._clear=!0,"straight"==r.get("lineType",e.get("lineType"))){const t=this.get("geometry");if(t){let e=t.coordinates;if(e){let i;"LineString"==t.type?i=[e]:"MultiLineString"==t.type&&(i=e),this.set("draw",(t=>{for(let e=0;e<i.length;e++){let o=i[e];if(o.length>0){const e=o[0],i=n.convert({longitude:e[0],latitude:e[1]});t.lineTo(i.x,i.y);for(let e=0;e<o.length;e++){const i=o[e],r=n.convert({longitude:i[0],latitude:i[1]});t.lineTo(r.x,r.y)}}}}))}}}else this.set("draw",(n=>{i&&!1===e.get("clipBack")&&i.clipAngle(180),a.context(this._display),a(t),a.context(null),i&&i.clipAngle&&i.clipAngle(o)}))}}}const e="linechanged";this.events.isEnabled(e)&&this.events.dispatch(e,{type:e,target:this})}}markDirtyProjection(){this.markDirty(),this._projectionDirty=!0}_clearDirty(){super._clearDirty(),this._projectionDirty=!1}_getTooltipPoint(){let t=this.get("tooltipX"),e=this.get("tooltipY"),n=0,i=0;if(s.isNumber(t)&&(n=t),s.isNumber(e)&&(i=e),t instanceof l.gG){const e=this.positionToGeoPoint(t.value),o=this.getPrivate("series");if(o){const t=o.chart;if(t){const o=t.convert(e);n=o.x,i=o.y}}}return{x:n,y:i}}positionToGeoPoint(t){const e=this.get("geometry"),n=this.getPrivate("series"),i=n.chart,o=this.dataItem;if(e&&n&&i&&o){const r=o.get("lineType",n.get("lineType"));let a,s,l,u=$(e),c=0,h=0,p=0,f=e.coordinates;if(f){let n;"LineString"==e.type?n=[f]:"MultiLineString"==e.type&&(n=f);for(let e=0;e<n.length;e++){let i=n[e];if(i.length>1){for(let o=1;o<i.length;o++)if(s=i[o-1],l=i[o],h=c/u,a=U(s,l),c+=a,p=c/u,h<=t&&p>t){e=n.length;break}}else 1==i.length&&(s=i[0],l=i[0],h=0,p=1)}if(s&&l){let e,n=(t-h)/(p-h);if("straight"==r){let t=i.convert({longitude:s[0],latitude:s[1]}),e=i.convert({longitude:l[0],latitude:l[1]}),o=t.x+(e.x-t.x)*n,r=t.y+(e.y-t.y)*n;return i.invert({x:o,y:r})}return e=function(t,e){var n=t[0]*v,i=t[1]*v,o=e[0]*v,r=e[1]*v,a=P(i),s=N(i),l=P(r),u=N(r),c=a*P(n),h=a*N(n),p=l*P(o),f=l*N(o),g=2*I(C(O(r-i)+a*l*O(o-n))),d=N(g),m=g?function(t){var e=N(t*=g)/d,n=N(g-t)/d,i=n*c+e*p,o=n*h+e*f,r=n*s+e*u;return[w(o,i)*y,w(r,C(i*i+o*o))*y]}:function(){return[n*y,i*y]};return m.distance=g,m}(s,l)(n),{longitude:e[0],latitude:e[1]}}}}return{longitude:0,latitude:0}}}Object.defineProperty(q,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapLine"}),Object.defineProperty(q,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:u.T.classNames.concat([q.className])});var K=n(7144),tt=n(5769);class et extends a{constructor(){super(...arguments),Object.defineProperty(this,"mapLines",{enumerable:!0,configurable:!0,writable:!0,value:new K.o(tt.YS.new({}),(()=>q._new(this._root,{},[this.mapLines.template])))}),Object.defineProperty(this,"_types",{enumerable:!0,configurable:!0,writable:!0,value:["LineString","MultiLineString"]})}_afterNew(){this.fields.push("lineType"),this._setRawDefault("lineTypeField","lineType"),super._afterNew()}makeMapLine(t){const e=this.children.push(this.mapLines.make());return e._setDataItem(t),this.mapLines.push(e),e}markDirtyProjection(){o.each(this.dataItems,(t=>{let e=t.get("mapLine");e&&e.markDirtyProjection()}))}_prepareChildren(){super._prepareChildren(),this.isDirty("stroke")&&this.mapLines.template.set("stroke",this.get("stroke"))}processDataItem(t){super.processDataItem(t);let e=t.get("mapLine");e||(e=this.makeMapLine(t)),this._handlePointsToConnect(t),t.on("pointsToConnect",(()=>{this._handlePointsToConnect(t)})),t.set("mapLine",e),this._addGeometry(t.get("geometry"),this),e.setPrivate("series",this)}_handlePointsToConnect(t){const e=t.get("pointsToConnect");e&&(o.each(e,(e=>{e.on("geometry",(()=>{this.markDirtyValues(t)})),e.on("longitude",(()=>{this.markDirtyValues(t)})),e.on("latitude",(()=>{this.markDirtyValues(t)}))})),this.markDirtyValues(t))}markDirtyValues(t){if(super.markDirtyValues(),t){const e=t.get("mapLine");if(e){const n=t.get("pointsToConnect");if(n){let i=[];o.each(n,(t=>{const e=t.get("longitude"),n=t.get("latitude");if(null!=e&&null!=n)i.push([e,n]);else{const e=t.get("geometry");if(e){const t=e.coordinates;t&&i.push([t[0],t[1]])}}}));let r={type:"LineString",coordinates:i};t.setRaw("geometry",r),e.set("geometry",r)}else e.set("geometry",t.get("geometry"))}}}disposeDataItem(t){super.disposeDataItem(t);const e=t.get("mapLine");e&&(this.mapLines.removeValue(e),e.dispose())}_excludeDataItem(t){super._excludeDataItem(t);const e=t.get("mapLine");e&&e.setPrivate("visible",!1)}_unexcludeDataItem(t){super._unexcludeDataItem(t);const e=t.get("mapLine");e&&e.setPrivate("visible",!0)}_notIncludeDataItem(t){super._notIncludeDataItem(t);const e=t.get("mapLine");e&&e.setPrivate("visible",!1)}_unNotIncludeDataItem(t){super._unNotIncludeDataItem(t);const e=t.get("mapLine");e&&e.setPrivate("visible",!0)}}function nt(t,e,n){t=+t,e=+e,n=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+n;for(var i=-1,o=0|Math.max(0,Math.ceil((e-t)/n)),r=new Array(o);++i<o;)r[i]=t+i*n;return r}function it(t,e,n){var i=nt(t,e-h,n).concat(e);return function(t){return i.map((function(e){return[t,e]}))}}function ot(t,e,n){var i=nt(t,e-h,n).concat(e);return function(t){return i.map((function(e){return[e,t]}))}}Object.defineProperty(et,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapLineSeries"}),Object.defineProperty(et,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:a.classNames.concat([et.className])});class rt extends et{constructor(){super(...arguments),Object.defineProperty(this,"_dataItem",{enumerable:!0,configurable:!0,writable:!0,value:this.makeDataItem({})})}_afterNew(){super._afterNew(),this.dataItems.push(this._dataItem),this._generate()}_updateChildren(){if(super._updateChildren(),this.isDirty("step")&&this._generate(),this.isDirty("clipExtent")&&this.get("clipExtent")){const t=this.chart;t&&t.events.on("geoboundschanged",(()=>{this._generate()})),this._generate()}}_generate(){let t=function(){var t,e,n,i,o,r,a,s,l,u,c,p,f=10,g=f,d=90,m=360,y=2.5;function v(){return{type:"MultiLineString",coordinates:b()}}function b(){return nt(x(i/d)*d,n,d).map(c).concat(nt(x(s/m)*m,a,m).map(p)).concat(nt(x(e/f)*f,t,f).filter((function(t){return _(t%d)>h})).map(l)).concat(nt(x(r/g)*g,o,g).filter((function(t){return _(t%m)>h})).map(u))}return v.lines=function(){return b().map((function(t){return{type:"LineString",coordinates:t}}))},v.outline=function(){return{type:"Polygon",coordinates:[c(i).concat(p(a).slice(1),c(n).reverse().slice(1),p(s).reverse().slice(1))]}},v.extent=function(t){return arguments.length?v.extentMajor(t).extentMinor(t):v.extentMinor()},v.extentMajor=function(t){return arguments.length?(i=+t[0][0],n=+t[1][0],s=+t[0][1],a=+t[1][1],i>n&&(t=i,i=n,n=t),s>a&&(t=s,s=a,a=t),v.precision(y)):[[i,s],[n,a]]},v.extentMinor=function(n){return arguments.length?(e=+n[0][0],t=+n[1][0],r=+n[0][1],o=+n[1][1],e>t&&(n=e,e=t,t=n),r>o&&(n=r,r=o,o=n),v.precision(y)):[[e,r],[t,o]]},v.step=function(t){return arguments.length?v.stepMajor(t).stepMinor(t):v.stepMinor()},v.stepMajor=function(t){return arguments.length?(d=+t[0],m=+t[1],v):[d,m]},v.stepMinor=function(t){return arguments.length?(f=+t[0],g=+t[1],v):[f,g]},v.precision=function(h){return arguments.length?(y=+h,l=it(r,o,90),u=ot(e,t,y),c=it(s,a,90),p=ot(i,n,y),v):y},v.extentMajor([[-180,-89.999999],[180,89.999999]]).extentMinor([[-180,-80.000001],[180,80.000001]])}();if(t){if(this.get("clipExtent")){const e=this.chart;if(e){const n=e.geoBounds();n&&t.extent([[n.left,n.bottom],[n.right,n.top]])}}const e=this.get("step",10);t.stepMinor([360,360]),t.stepMajor([e,e]),this._dataItem.set("geometry",t())}}}Object.defineProperty(rt,"className",{enumerable:!0,configurable:!0,writable:!0,value:"GraticuleSeries"}),Object.defineProperty(rt,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:et.classNames.concat([rt.className])});var at=n(3409),st=n(3783);function lt(t,e){function n(n,i){return n=t(n,i),e(n[0],n[1])}return t.invert&&e.invert&&(n.invert=function(n,i){return(n=e.invert(n,i))&&t.invert(n[0],n[1])}),n}function ut(t,e){return _(t)>f&&(t-=Math.round(t/m)*m),[t,e]}function ct(t,e,n){return(t%=m)?e||n?lt(pt(t),ft(e,n)):pt(t):e||n?ft(e,n):ut}function ht(t){return function(e,n){return _(e+=t)>f&&(e-=Math.round(e/m)*m),[e,n]}}function pt(t){var e=ht(t);return e.invert=ht(-t),e}function ft(t,e){var n=P(t),i=N(t),o=P(e),r=N(e);function a(t,e){var a=P(e),s=P(t)*a,l=N(t)*a,u=N(e),c=u*n+s*i;return[w(l*o-c*r,s*n-u*i),I(c*o+l*r)]}return a.invert=function(t,e){var a=P(e),s=P(t)*a,l=N(t)*a,u=N(e),c=u*o-l*r;return[w(l*o+u*r,s*n+c*i),I(c*n-s*i)]},a}function gt(){var t,e=[];return{point:function(e,n,i){t.push([e,n,i])},lineStart:function(){e.push(t=[])},lineEnd:T,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function dt(t,e){return _(t[0]-e[0])<h&&_(t[1]-e[1])<h}function mt(t,e,n,i){this.x=t,this.z=e,this.o=n,this.e=i,this.v=!1,this.n=this.p=null}function yt(t,e,n,i,o){var r,a,s=[],l=[];if(t.forEach((function(t){if(!((e=t.length-1)<=0)){var e,n,i=t[0],a=t[e];if(dt(i,a)){if(!i[2]&&!a[2]){for(o.lineStart(),r=0;r<e;++r)o.point((i=t[r])[0],i[1]);return void o.lineEnd()}a[0]+=2e-6}s.push(n=new mt(i,t,null,!0)),l.push(n.o=new mt(i,null,n,!1)),s.push(n=new mt(a,t,null,!1)),l.push(n.o=new mt(a,null,n,!0))}})),s.length){for(l.sort(e),vt(s),vt(l),r=0,a=l.length;r<a;++r)l[r].e=n=!n;for(var u,c,h=s[0];;){for(var p=h,f=!0;p.v;)if((p=p.n)===h)return;u=p.z,o.lineStart();do{if(p.v=p.o.v=!0,p.e){if(f)for(r=0,a=u.length;r<a;++r)o.point((c=u[r])[0],c[1]);else i(p.x,p.n.x,1,o);p=p.n}else{if(f)for(u=p.p.z,r=u.length-1;r>=0;--r)o.point((c=u[r])[0],c[1]);else i(p.x,p.p.x,-1,o);p=p.p}u=(p=p.o).z,f=!f}while(!p.v);o.lineEnd()}}}function vt(t){if(e=t.length){for(var e,n,i=0,o=t[0];++i<e;)o.n=n=t[i],n.p=o,o=n;o.n=n=t[0],n.p=o}}function _t(t){return[w(t[1],t[0]),I(t[2])]}function bt(t){var e=t[0],n=t[1],i=P(n);return[i*P(e),i*N(e),N(n)]}function wt(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Pt(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function xt(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function Dt(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function St(t){var e=C(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}function Mt(t){return _(t[0])<=f?t[0]:j(t[0])*((_(t[0])+f)%m-f)}function Nt(t){return Array.from(function*(t){for(const e of t)yield*e}(t))}function jt(t,e,n,i){return function(o){var r,a,s,l=e(o),u=gt(),p=e(u),y=!1,v={point:_,lineStart:x,lineEnd:D,polygonStart:function(){v.point=S,v.lineStart=M,v.lineEnd=j,a=[],r=[]},polygonEnd:function(){v.point=_,v.lineStart=x,v.lineEnd=D,a=Nt(a);var t=function(t,e){var n=Mt(e),i=e[1],o=N(i),r=[N(n),-P(n),0],a=0,s=0,l=new c;1===o?i=g+h:-1===o&&(i=-g-h);for(var u=0,p=t.length;u<p;++u)if(v=(y=t[u]).length)for(var y,v,_=y[v-1],b=Mt(_),x=_[1]/2+d,D=N(x),S=P(x),M=0;M<v;++M,b=C,D=O,S=T,_=j){var j=y[M],C=Mt(j),E=j[1]/2+d,O=N(E),T=P(E),L=C-b,k=L>=0?1:-1,z=k*L,R=z>f,G=D*O;if(l.add(w(G*k*N(z),S*T+G*P(z))),a+=R?L+k*m:L,R^b>=n^C>=n){var Y=Pt(bt(_),bt(j));St(Y);var B=Pt(r,Y);St(B);var X=(R^L>=0?-1:1)*I(B[2]);(i>X||i===X&&(Y[0]||Y[1]))&&(s+=R^L>=0?1:-1)}}return(a<-h||a<h&&l<-1e-12)^1&s}(r,i);a.length?(y||(o.polygonStart(),y=!0),yt(a,Et,t,n,o)):t&&(y||(o.polygonStart(),y=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),y&&(o.polygonEnd(),y=!1),a=r=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function _(e,n){t(e,n)&&o.point(e,n)}function b(t,e){l.point(t,e)}function x(){v.point=b,l.lineStart()}function D(){v.point=_,l.lineEnd()}function S(t,e){s.push([t,e]),p.point(t,e)}function M(){p.lineStart(),s=[]}function j(){S(s[0][0],s[0][1]),p.lineEnd();var t,e,n,i,l=p.clean(),c=u.result(),h=c.length;if(s.pop(),r.push(s),s=null,h)if(1&l){if((e=(n=c[0]).length-1)>0){for(y||(o.polygonStart(),y=!0),o.lineStart(),t=0;t<e;++t)o.point((i=n[t])[0],i[1]);o.lineEnd()}}else h>1&&2&l&&c.push(c.pop().concat(c.shift())),a.push(c.filter(Ct))}return v}}function Ct(t){return t.length>1}function Et(t,e){return((t=t.x)[0]<0?t[1]-g-h:g-t[1])-((e=e.x)[0]<0?e[1]-g-h:g-e[1])}ut.invert=ut;var It=jt((function(){return!0}),(function(t){var e,n=NaN,i=NaN,o=NaN;return{lineStart:function(){t.lineStart(),e=1},point:function(r,a){var s=r>0?f:-f,l=_(r-n);_(l-f)<h?(t.point(n,i=(i+a)/2>0?g:-g),t.point(o,i),t.lineEnd(),t.lineStart(),t.point(s,i),t.point(r,i),e=0):o!==s&&l>=f&&(_(n-o)<h&&(n-=o*h),_(r-s)<h&&(r-=s*h),i=function(t,e,n,i){var o,r,a=N(t-n);return _(a)>h?b((N(e)*(r=P(i))*N(n)-N(i)*(o=P(e))*N(t))/(o*r*a)):(e+i)/2}(n,i,r,a),t.point(o,i),t.lineEnd(),t.lineStart(),t.point(s,i),e=0),t.point(n=r,i=a),o=s},lineEnd:function(){t.lineEnd(),n=i=NaN},clean:function(){return 2-e}}}),(function(t,e,n,i){var o;if(null==t)o=n*g,i.point(-f,o),i.point(0,o),i.point(f,o),i.point(f,0),i.point(f,-o),i.point(0,-o),i.point(-f,-o),i.point(-f,0),i.point(-f,o);else if(_(t[0]-e[0])>h){var r=t[0]<e[0]?f:-f;o=n*r/2,i.point(-r,o),i.point(0,o),i.point(r,o)}else i.point(e[0],e[1])}),[-f,-g]);function Ot(t){return function(){return t}}function Tt(t,e,n,i,o,r){if(n){var a=P(e),s=N(e),l=i*n;null==o?(o=e+i*m,r=e-l/2):(o=Lt(a,o),r=Lt(a,r),(i>0?o<r:o>r)&&(o+=i*m));for(var u,c=o;i>0?c>r:c<r;c-=l)u=_t([a,-s*P(c),-s*N(c)]),t.point(u[0],u[1])}}function Lt(t,e){(e=bt(e))[0]-=t,St(e);var n,i=(n=-e[1])>1?0:n<-1?f:Math.acos(n);return((-e[2]<0?-i:i)+m-h)%m}function kt(t){var e=P(t),n=6*v,i=e>0,o=_(e)>h;function r(t,n){return P(t)*P(n)>e}function a(t,n,i){var o=[1,0,0],r=Pt(bt(t),bt(n)),a=wt(r,r),s=r[0],l=a-s*s;if(!l)return!i&&t;var u=e*a/l,c=-e*s/l,p=Pt(o,r),g=Dt(o,u);xt(g,Dt(r,c));var d=p,m=wt(g,d),y=wt(d,d),v=m*m-y*(wt(g,g)-1);if(!(v<0)){var b=C(v),w=Dt(d,(-m-b)/y);if(xt(w,g),w=_t(w),!i)return w;var P,x=t[0],D=n[0],S=t[1],M=n[1];D<x&&(P=x,x=D,D=P);var N=D-x,j=_(N-f)<h;if(!j&&M<S&&(P=S,S=M,M=P),j||N<h?j?S+M>0^w[1]<(_(w[0]-x)<h?S:M):S<=w[1]&&w[1]<=M:N>f^(x<=w[0]&&w[0]<=D)){var E=Dt(d,(-m+b)/y);return xt(E,g),[w,_t(E)]}}}function s(e,n){var o=i?t:f-t,r=0;return e<-o?r|=1:e>o&&(r|=2),n<-o?r|=4:n>o&&(r|=8),r}return jt(r,(function(t){var e,n,l,u,c;return{lineStart:function(){u=l=!1,c=1},point:function(h,p){var g,d=[h,p],m=r(h,p),y=i?m?0:s(h,p):m?s(h+(h<0?f:-f),p):0;if(!e&&(u=l=m)&&t.lineStart(),m!==l&&(!(g=a(e,d))||dt(e,g)||dt(d,g))&&(d[2]=1),m!==l)c=0,m?(t.lineStart(),g=a(d,e),t.point(g[0],g[1])):(g=a(e,d),t.point(g[0],g[1],2),t.lineEnd()),e=g;else if(o&&e&&i^m){var v;y&n||!(v=a(d,e,!0))||(c=0,i?(t.lineStart(),t.point(v[0][0],v[0][1]),t.point(v[1][0],v[1][1]),t.lineEnd()):(t.point(v[1][0],v[1][1]),t.lineEnd(),t.lineStart(),t.point(v[0][0],v[0][1],3)))}!m||e&&dt(e,d)||t.point(d[0],d[1]),e=d,l=m,n=y},lineEnd:function(){l&&t.lineEnd(),e=null},clean:function(){return c|(u&&l)<<1}}}),(function(e,i,o,r){Tt(r,t,n,o,e,i)}),i?[0,-t]:[-f,t-f])}var zt=1e9,Rt=-zt;function Gt(t,e,n,i){function o(o,r){return t<=o&&o<=n&&e<=r&&r<=i}function r(o,r,s,u){var c=0,h=0;if(null==o||(c=a(o,s))!==(h=a(r,s))||l(o,r)<0^s>0)do{u.point(0===c||3===c?t:n,c>1?i:e)}while((c=(c+s+4)%4)!==h);else u.point(r[0],r[1])}function a(i,o){return _(i[0]-t)<h?o>0?0:3:_(i[0]-n)<h?o>0?2:1:_(i[1]-e)<h?o>0?1:0:o>0?3:2}function s(t,e){return l(t.x,e.x)}function l(t,e){var n=a(t,1),i=a(e,1);return n!==i?n-i:0===n?e[1]-t[1]:1===n?t[0]-e[0]:2===n?t[1]-e[1]:e[0]-t[0]}return function(a){var l,u,c,h,p,f,g,d,m,y,v,_=a,b=gt(),w={point:P,lineStart:function(){w.point=x,u&&u.push(c=[]),y=!0,m=!1,g=d=NaN},lineEnd:function(){l&&(x(h,p),f&&m&&b.rejoin(),l.push(b.result())),w.point=P,m&&_.lineEnd()},polygonStart:function(){_=b,l=[],u=[],v=!0},polygonEnd:function(){var e=function(){for(var e=0,n=0,o=u.length;n<o;++n)for(var r,a,s=u[n],l=1,c=s.length,h=s[0],p=h[0],f=h[1];l<c;++l)r=p,a=f,p=(h=s[l])[0],f=h[1],a<=i?f>i&&(p-r)*(i-a)>(f-a)*(t-r)&&++e:f<=i&&(p-r)*(i-a)<(f-a)*(t-r)&&--e;return e}(),n=v&&e,o=(l=Nt(l)).length;(n||o)&&(a.polygonStart(),n&&(a.lineStart(),r(null,null,1,a),a.lineEnd()),o&&yt(l,s,e,r,a),a.polygonEnd()),_=a,l=u=c=null}};function P(t,e){o(t,e)&&_.point(t,e)}function x(r,a){var s=o(r,a);if(u&&c.push([r,a]),y)h=r,p=a,f=s,y=!1,s&&(_.lineStart(),_.point(r,a));else if(s&&m)_.point(r,a);else{var l=[g=Math.max(Rt,Math.min(zt,g)),d=Math.max(Rt,Math.min(zt,d))],b=[r=Math.max(Rt,Math.min(zt,r)),a=Math.max(Rt,Math.min(zt,a))];!function(t,e,n,i,o,r){var a,s=t[0],l=t[1],u=0,c=1,h=e[0]-s,p=e[1]-l;if(a=n-s,h||!(a>0)){if(a/=h,h<0){if(a<u)return;a<c&&(c=a)}else if(h>0){if(a>c)return;a>u&&(u=a)}if(a=o-s,h||!(a<0)){if(a/=h,h<0){if(a>c)return;a>u&&(u=a)}else if(h>0){if(a<u)return;a<c&&(c=a)}if(a=i-l,p||!(a>0)){if(a/=p,p<0){if(a<u)return;a<c&&(c=a)}else if(p>0){if(a>c)return;a>u&&(u=a)}if(a=r-l,p||!(a<0)){if(a/=p,p<0){if(a>c)return;a>u&&(u=a)}else if(p>0){if(a<u)return;a<c&&(c=a)}return u>0&&(t[0]=s+u*h,t[1]=l+u*p),c<1&&(e[0]=s+c*h,e[1]=l+c*p),!0}}}}}(l,b,t,e,n,i)?s&&(_.lineStart(),_.point(r,a),v=!1):(m||(_.lineStart(),_.point(l[0],l[1])),_.point(b[0],b[1]),s||_.lineEnd(),v=!1)}g=r,d=a,m=s}return w}}var Yt=t=>t;function Bt(t){return function(e){var n=new Xt;for(var i in t)n[i]=t[i];return n.stream=e,n}}function Xt(){}Xt.prototype={constructor:Xt,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var At=1/0,Zt=At,Wt=-At,Ft=Wt,Vt={point:function(t,e){t<At&&(At=t),t>Wt&&(Wt=t),e<Zt&&(Zt=e),e>Ft&&(Ft=e)},lineStart:T,lineEnd:T,polygonStart:T,polygonEnd:T,result:function(){var t=[[At,Zt],[Wt,Ft]];return Wt=Ft=-(Zt=At=1/0),t}},Jt=Vt;function $t(t,e,n){var i=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=i&&t.clipExtent(null),Z(n,t.stream(Jt)),e(Jt.result()),null!=i&&t.clipExtent(i),t}function Ht(t,e,n){return $t(t,(function(n){var i=e[1][0]-e[0][0],o=e[1][1]-e[0][1],r=Math.min(i/(n[1][0]-n[0][0]),o/(n[1][1]-n[0][1])),a=+e[0][0]+(i-r*(n[1][0]+n[0][0]))/2,s=+e[0][1]+(o-r*(n[1][1]+n[0][1]))/2;t.scale(150*r).translate([a,s])}),n)}function Qt(t,e,n){return Ht(t,[[0,0],e],n)}function Ut(t,e,n){return $t(t,(function(n){var i=+e,o=i/(n[1][0]-n[0][0]),r=(i-o*(n[1][0]+n[0][0]))/2,a=-o*n[0][1];t.scale(150*o).translate([r,a])}),n)}function qt(t,e,n){return $t(t,(function(n){var i=+e,o=i/(n[1][1]-n[0][1]),r=-o*n[0][0],a=(i-o*(n[1][1]+n[0][1]))/2;t.scale(150*o).translate([r,a])}),n)}var Kt=P(30*v);function te(t,e){return+e?function(t,e){function n(i,o,r,a,s,l,u,c,p,f,g,d,m,y){var v=u-i,b=c-o,P=v*v+b*b;if(P>4*e&&m--){var x=a+f,D=s+g,S=l+d,M=C(x*x+D*D+S*S),N=I(S/=M),j=_(_(S)-1)<h||_(r-p)<h?(r+p)/2:w(D,x),E=t(j,N),O=E[0],T=E[1],L=O-i,k=T-o,z=b*L-v*k;(z*z/P>e||_((v*L+b*k)/P-.5)>.3||a*f+s*g+l*d<Kt)&&(n(i,o,r,a,s,l,O,T,j,x/=M,D/=M,S,m,y),y.point(O,T),n(O,T,j,x,D,S,u,c,p,f,g,d,m,y))}}return function(e){var i,o,r,a,s,l,u,c,h,p,f,g,d={point:m,lineStart:y,lineEnd:_,polygonStart:function(){e.polygonStart(),d.lineStart=b},polygonEnd:function(){e.polygonEnd(),d.lineStart=y}};function m(n,i){n=t(n,i),e.point(n[0],n[1])}function y(){c=NaN,d.point=v,e.lineStart()}function v(i,o){var r=bt([i,o]),a=t(i,o);n(c,h,u,p,f,g,c=a[0],h=a[1],u=i,p=r[0],f=r[1],g=r[2],16,e),e.point(c,h)}function _(){d.point=m,e.lineEnd()}function b(){y(),d.point=w,d.lineEnd=P}function w(t,e){v(i=t,e),o=c,r=h,a=p,s=f,l=g,d.point=v}function P(){n(c,h,u,p,f,g,o,r,i,a,s,l,16,e),d.lineEnd=_,_()}return d}}(t,e):function(t){return Bt({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}(t)}var ee=Bt({point:function(t,e){this.stream.point(t*v,e*v)}});function ne(t,e,n,i,o,r){if(!r)return function(t,e,n,i,o){function r(r,a){return[e+t*(r*=i),n-t*(a*=o)]}return r.invert=function(r,a){return[(r-e)/t*i,(n-a)/t*o]},r}(t,e,n,i,o);var a=P(r),s=N(r),l=a*t,u=s*t,c=a/t,h=s/t,p=(s*n-a*e)/t,f=(s*e+a*n)/t;function g(t,r){return[l*(t*=i)-u*(r*=o)+e,n-u*t-l*r]}return g.invert=function(t,e){return[i*(c*t-h*e+p),o*(f-h*t-c*e)]},g}function ie(t){return oe((function(){return t}))()}function oe(t){var e,n,i,o,r,a,s,l,u,c,h=150,p=480,f=250,g=0,d=0,m=0,_=0,b=0,w=0,P=1,x=1,D=null,S=It,M=null,N=Yt,j=.5;function E(t){return l(t[0]*v,t[1]*v)}function I(t){return(t=l.invert(t[0],t[1]))&&[t[0]*y,t[1]*y]}function O(){var t=ne(h,0,0,P,x,w).apply(null,e(g,d)),i=ne(h,p-t[0],f-t[1],P,x,w);return n=ct(m,_,b),s=lt(e,i),l=lt(n,s),a=te(s,j),T()}function T(){return u=c=null,E}return E.stream=function(t){return u&&c===t?u:u=ee(function(t){return Bt({point:function(e,n){var i=t(e,n);return this.stream.point(i[0],i[1])}})}(n)(S(a(N(c=t)))))},E.preclip=function(t){return arguments.length?(S=t,D=void 0,T()):S},E.postclip=function(t){return arguments.length?(N=t,M=i=o=r=null,T()):N},E.clipAngle=function(t){return arguments.length?(S=+t?kt(D=t*v):(D=null,It),T()):D*y},E.clipExtent=function(t){return arguments.length?(N=null==t?(M=i=o=r=null,Yt):Gt(M=+t[0][0],i=+t[0][1],o=+t[1][0],r=+t[1][1]),T()):null==M?null:[[M,i],[o,r]]},E.scale=function(t){return arguments.length?(h=+t,O()):h},E.translate=function(t){return arguments.length?(p=+t[0],f=+t[1],O()):[p,f]},E.center=function(t){return arguments.length?(g=t[0]%360*v,d=t[1]%360*v,O()):[g*y,d*y]},E.rotate=function(t){return arguments.length?(m=t[0]%360*v,_=t[1]%360*v,b=t.length>2?t[2]%360*v:0,O()):[m*y,_*y,b*y]},E.angle=function(t){return arguments.length?(w=t%360*v,O()):w*y},E.reflectX=function(t){return arguments.length?(P=t?-1:1,O()):P<0},E.reflectY=function(t){return arguments.length?(x=t?-1:1,O()):x<0},E.precision=function(t){return arguments.length?(a=te(s,j=t*t),T()):C(j)},E.fitExtent=function(t,e){return Ht(E,t,e)},E.fitSize=function(t,e){return Qt(E,t,e)},E.fitWidth=function(t,e){return Ut(E,t,e)},E.fitHeight=function(t,e){return qt(E,t,e)},function(){return e=t.apply(this,arguments),E.invert=e.invert&&I,O()}}function re(t,e){return[t,M(E((g+e)/2))]}function ae(){return function(t){var e,n,i,o=ie(t),r=o.center,a=o.scale,s=o.translate,l=o.clipExtent,u=null;function c(){var r=f*a(),s=o(function(t){function e(e){return(e=t(e[0]*v,e[1]*v))[0]*=y,e[1]*=y,e}return t=ct(t[0]*v,t[1]*v,t.length>2?t[2]*v:0),e.invert=function(e){return(e=t.invert(e[0]*v,e[1]*v))[0]*=y,e[1]*=y,e},e}(o.rotate()).invert([0,0]));return l(null==u?[[s[0]-r,s[1]-r],[s[0]+r,s[1]+r]]:t===re?[[Math.max(s[0]-r,u),e],[Math.min(s[0]+r,n),i]]:[[u,Math.max(s[1]-r,e)],[n,Math.min(s[1]+r,i)]])}return o.scale=function(t){return arguments.length?(a(t),c()):a()},o.translate=function(t){return arguments.length?(s(t),c()):s()},o.center=function(t){return arguments.length?(r(t),c()):r()},o.clipExtent=function(t){return arguments.length?(null==t?u=e=n=i=null:(u=+t[0][0],e=+t[0][1],n=+t[1][0],i=+t[1][1]),c()):null==u?null:[[u,e],[n,i]]},c()}(re).scale(961/m)}re.invert=function(t,e){return[t,2*b(D(e))-g]};var se=n(9395);class le extends at.Q{setupDefaultRules(){super.setupDefaultRules();const t=this._root.interfaceColors,e=this.rule.bind(this);e("MapChart").setAll({projection:ae(),panX:"translateX",panY:"translateY",pinchZoom:!0,zoomStep:2,zoomLevel:1,rotationX:0,rotationY:0,rotationZ:0,maxZoomLevel:32,minZoomLevel:1,wheelY:"zoom",wheelX:"none",animationEasing:se.out(se.cubic),wheelEasing:se.out(se.cubic),wheelDuration:0,wheelSensitivity:1,maxPanOut:.4,centerMapOnZoomOut:!0});{const n=e("MapLine");n.setAll({precision:.5,role:"figure"}),(0,st.v)(n,"stroke",t,"grid")}e("MapPolygonSeries").setAll({affectsBounds:!0}),e("MapPointSeries").setAll({affectsBounds:!1,clipFront:!1,clipBack:!0,autoScale:!1}),e("MapLineSeries").setAll({affectsBounds:!1});{const n=e("MapPolygon");n.setAll({precision:.5,isMeasured:!1,role:"figure",fillOpacity:1,position:"absolute",strokeWidth:.2,strokeOpacity:1}),(0,st.v)(n,"fill",t,"primaryButton"),(0,st.v)(n,"stroke",t,"background")}e("Button",["zoomcontrol"]).setAll({marginTop:1,marginBottom:1}),e("Graphics",["map","button","plus","icon"]).setAll({x:l.CI,y:l.CI,draw:t=>{t.moveTo(-4,0),t.lineTo(4,0),t.moveTo(0,-4),t.lineTo(0,4)}}),e("Graphics",["map","button","minus","icon"]).setAll({x:l.CI,y:l.CI,draw:t=>{t.moveTo(-4,0),t.lineTo(4,0)}}),e("GraticuleSeries").setAll({step:10}),e("ZoomControl").setAll({x:l.AQ,centerX:l.AQ,y:l.AQ,centerY:l.AQ,paddingRight:10,paddingBottom:10})}}var ue,ce,he,pe,fe=n(5829),ge=n(7142),de=new c,me=new c,ye={point:T,lineStart:T,lineEnd:T,polygonStart:function(){ye.lineStart=ve,ye.lineEnd=we},polygonEnd:function(){ye.lineStart=ye.lineEnd=ye.point=T,de.add(_(me)),me=new c},result:function(){var t=de/2;return de=new c,t}};function ve(){ye.point=_e}function _e(t,e){ye.point=be,ue=he=t,ce=pe=e}function be(t,e){me.add(pe*t-he*e),he=t,pe=e}function we(){be(ue,ce)}var Pe,xe,De,Se,Me=ye,Ne=0,je=0,Ce=0,Ee=0,Ie=0,Oe=0,Te=0,Le=0,ke=0,ze={point:Re,lineStart:Ge,lineEnd:Xe,polygonStart:function(){ze.lineStart=Ae,ze.lineEnd=Ze},polygonEnd:function(){ze.point=Re,ze.lineStart=Ge,ze.lineEnd=Xe},result:function(){var t=ke?[Te/ke,Le/ke]:Oe?[Ee/Oe,Ie/Oe]:Ce?[Ne/Ce,je/Ce]:[NaN,NaN];return Ne=je=Ce=Ee=Ie=Oe=Te=Le=ke=0,t}};function Re(t,e){Ne+=t,je+=e,++Ce}function Ge(){ze.point=Ye}function Ye(t,e){ze.point=Be,Re(De=t,Se=e)}function Be(t,e){var n=t-De,i=e-Se,o=C(n*n+i*i);Ee+=o*(De+t)/2,Ie+=o*(Se+e)/2,Oe+=o,Re(De=t,Se=e)}function Xe(){ze.point=Re}function Ae(){ze.point=We}function Ze(){Fe(Pe,xe)}function We(t,e){ze.point=Fe,Re(Pe=De=t,xe=Se=e)}function Fe(t,e){var n=t-De,i=e-Se,o=C(n*n+i*i);Ee+=o*(De+t)/2,Ie+=o*(Se+e)/2,Oe+=o,Te+=(o=Se*t-De*e)*(De+t),Le+=o*(Se+e),ke+=3*o,Re(De=t,Se=e)}var Ve=ze;function Je(t){this._context=t}Je.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._context.moveTo(t,e),this._point=1;break;case 1:this._context.lineTo(t,e);break;default:this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,m)}},result:T};var $e,He,Qe,Ue,qe,Ke=new c,tn={point:T,lineStart:function(){tn.point=en},lineEnd:function(){$e&&nn(He,Qe),tn.point=T},polygonStart:function(){$e=!0},polygonEnd:function(){$e=null},result:function(){var t=+Ke;return Ke=new c,t}};function en(t,e){tn.point=nn,He=Ue=t,Qe=qe=e}function nn(t,e){Ue-=t,qe-=e,Ke.add(C(Ue*Ue+qe*qe)),Ue=t,qe=e}var on=tn;let rn,an,sn,ln;class un{constructor(t){this._append=null==t?cn:function(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return cn;if(e!==rn){const t=10**e;rn=e,an=function(e){let n=1;this._+=e[0];for(const i=e.length;n<i;++n)this._+=Math.round(arguments[n]*t)/t+e[n]}}return an}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,e){switch(this._point){case 0:this._append`M${t},${e}`,this._point=1;break;case 1:this._append`L${t},${e}`;break;default:if(this._append`M${t},${e}`,this._radius!==sn||this._append!==an){const t=this._radius,e=this._;this._="",this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`,sn=t,an=this._append,ln=this._,this._=e}this._+=ln}}result(){const t=this._;return this._="",t.length?t:null}}function cn(t){let e=1;this._+=t[0];for(const n=t.length;e<n;++e)this._+=arguments[e]+t[e]}var hn,pn,fn,gn,dn,mn,yn,vn,_n,bn,wn,Pn,xn,Dn,Sn,Mn,Nn=n(1112),jn=n(3145),Cn=n(751),En={sphere:T,point:In,lineStart:Tn,lineEnd:zn,polygonStart:function(){En.lineStart=Rn,En.lineEnd=Gn},polygonEnd:function(){En.lineStart=Tn,En.lineEnd=zn}};function In(t,e){t*=v;var n=P(e*=v);On(n*P(t),n*N(t),N(e))}function On(t,e,n){++hn,fn+=(t-fn)/hn,gn+=(e-gn)/hn,dn+=(n-dn)/hn}function Tn(){En.point=Ln}function Ln(t,e){t*=v;var n=P(e*=v);Dn=n*P(t),Sn=n*N(t),Mn=N(e),En.point=kn,On(Dn,Sn,Mn)}function kn(t,e){t*=v;var n=P(e*=v),i=n*P(t),o=n*N(t),r=N(e),a=w(C((a=Sn*r-Mn*o)*a+(a=Mn*i-Dn*r)*a+(a=Dn*o-Sn*i)*a),Dn*i+Sn*o+Mn*r);pn+=a,mn+=a*(Dn+(Dn=i)),yn+=a*(Sn+(Sn=o)),vn+=a*(Mn+(Mn=r)),On(Dn,Sn,Mn)}function zn(){En.point=In}function Rn(){En.point=Yn}function Gn(){Bn(Pn,xn),En.point=In}function Yn(t,e){Pn=t,xn=e,t*=v,e*=v,En.point=Bn;var n=P(e);Dn=n*P(t),Sn=n*N(t),Mn=N(e),On(Dn,Sn,Mn)}function Bn(t,e){t*=v;var n=P(e*=v),i=n*P(t),o=n*N(t),r=N(e),a=Sn*r-Mn*o,s=Mn*i-Dn*r,l=Dn*o-Sn*i,u=S(a,s,l),c=I(u),h=u&&-c/u;_n.add(h*a),bn.add(h*s),wn.add(h*l),pn+=c,mn+=c*(Dn+(Dn=i)),yn+=c*(Sn+(Sn=o)),vn+=c*(Mn+(Mn=r)),On(Dn,Sn,Mn)}var Xn,An,Zn,Wn,Fn,Vn,Jn,$n,Hn,Qn,Un,qn,Kn,ti,ei,ni,ii=new c,oi=new c,ri={point:T,lineStart:T,lineEnd:T,polygonStart:function(){ii=new c,ri.lineStart=ai,ri.lineEnd=si},polygonEnd:function(){var t=+ii;oi.add(t<0?m+t:t),this.lineStart=this.lineEnd=this.point=T},sphere:function(){oi.add(m)}};function ai(){ri.point=li}function si(){ui(Xn,An)}function li(t,e){ri.point=ui,Xn=t,An=e,Zn=t*=v,Wn=P(e=(e*=v)/2+d),Fn=N(e)}function ui(t,e){var n=(t*=v)-Zn,i=n>=0?1:-1,o=i*n,r=P(e=(e*=v)/2+d),a=N(e),s=Fn*a,l=Wn*r+s*P(o),u=s*i*N(o);ii.add(w(u,l)),Zn=t,Wn=r,Fn=a}function ci(t){return oi=new c,Z(t,ri),2*oi}var hi={point:pi,lineStart:gi,lineEnd:di,polygonStart:function(){hi.point=mi,hi.lineStart=yi,hi.lineEnd=vi,ti=new c,ri.polygonStart()},polygonEnd:function(){ri.polygonEnd(),hi.point=pi,hi.lineStart=gi,hi.lineEnd=di,ii<0?(Vn=-($n=180),Jn=-(Hn=90)):ti>h?Hn=90:ti<-h&&(Jn=-90),ni[0]=Vn,ni[1]=$n},sphere:function(){Vn=-($n=180),Jn=-(Hn=90)}};function pi(t,e){ei.push(ni=[Vn=t,$n=t]),e<Jn&&(Jn=e),e>Hn&&(Hn=e)}function fi(t,e){var n=bt([t*v,e*v]);if(Kn){var i=Pt(Kn,n),o=Pt([i[1],-i[0],0],i);St(o),o=_t(o);var r,a=t-Qn,s=a>0?1:-1,l=o[0]*y*s,u=_(a)>180;u^(s*Qn<l&&l<s*t)?(r=o[1]*y)>Hn&&(Hn=r):u^(s*Qn<(l=(l+360)%360-180)&&l<s*t)?(r=-o[1]*y)<Jn&&(Jn=r):(e<Jn&&(Jn=e),e>Hn&&(Hn=e)),u?t<Qn?_i(Vn,t)>_i(Vn,$n)&&($n=t):_i(t,$n)>_i(Vn,$n)&&(Vn=t):$n>=Vn?(t<Vn&&(Vn=t),t>$n&&($n=t)):t>Qn?_i(Vn,t)>_i(Vn,$n)&&($n=t):_i(t,$n)>_i(Vn,$n)&&(Vn=t)}else ei.push(ni=[Vn=t,$n=t]);e<Jn&&(Jn=e),e>Hn&&(Hn=e),Kn=n,Qn=t}function gi(){hi.point=fi}function di(){ni[0]=Vn,ni[1]=$n,hi.point=pi,Kn=null}function mi(t,e){if(Kn){var n=t-Qn;ti.add(_(n)>180?n+(n>0?360:-360):n)}else Un=t,qn=e;ri.point(t,e),fi(t,e)}function yi(){ri.lineStart()}function vi(){mi(Un,qn),ri.lineEnd(),_(ti)>h&&(Vn=-($n=180)),ni[0]=Vn,ni[1]=$n,Kn=null}function _i(t,e){return(e-=t)<0?e+360:e}function bi(t,e){return t[0]-e[0]}function wi(t,e){return t[0]<=t[1]?t[0]<=e&&e<=t[1]:e<t[0]||t[1]<e}function Pi(t,e){return function(){var t,e,n=Ot([0,0]),i=Ot(90),o=Ot(6),r={point:function(n,i){t.push(n=e(n,i)),n[0]*=y,n[1]*=y}};function a(){var a=n.apply(this,arguments),s=i.apply(this,arguments)*v,l=o.apply(this,arguments)*v;return t=[],e=ct(-a[0]*v,-a[1]*v,0).invert,Tt(r,s,l,1),a={type:"Polygon",coordinates:[t]},t=e=null,a}return a.center=function(t){return arguments.length?(n="function"==typeof t?t:Ot([+t[0],+t[1]]),a):n},a.radius=function(t){return arguments.length?(i="function"==typeof t?t:Ot(+t),a):i},a.precision=function(t){return arguments.length?(o="function"==typeof t?t:Ot(+t),a):o},a}().center([t.longitude,t.latitude]).radius(e)()}function xi(t){const e=function(t){hn=pn=fn=gn=dn=mn=yn=vn=0,_n=new c,bn=new c,wn=new c,Z(t,En);var e=+_n,n=+bn,i=+wn,o=S(e,n,i);return o<p&&(e=mn,n=yn,i=vn,pn<h&&(e=fn,n=gn,i=dn),(o=S(e,n,i))<p)?[NaN,NaN]:[w(n,e)*y,I(i/o)*y]}(t);return{longitude:e[0],latitude:e[1]}}function Di(t){return ci(t)}function Si(t){const e=function(t){var e,n,i,o,r,a,s;if(Hn=$n=-(Vn=Jn=1/0),ei=[],Z(t,hi),n=ei.length){for(ei.sort(bi),e=1,r=[i=ei[0]];e<n;++e)wi(i,(o=ei[e])[0])||wi(i,o[1])?(_i(i[0],o[1])>_i(i[0],i[1])&&(i[1]=o[1]),_i(o[0],i[1])>_i(i[0],i[1])&&(i[0]=o[0])):r.push(i=o);for(a=-1/0,e=0,i=r[n=r.length-1];e<=n;i=o,++e)o=r[e],(s=_i(i[1],o[0]))>a&&(a=s,Vn=o[0],$n=i[1])}return ei=ni=null,Vn===1/0||Jn===1/0?[[NaN,NaN],[NaN,NaN]]:[[Vn,Jn],[$n,Hn]]}(t);if(e){const t={left:e[0][0],right:e[1][0],top:e[1][1],bottom:e[0][1]};return t.right<t.left&&(t.right=180,t.left=-180),t}return{left:0,right:0,top:0,bottom:0}}function Mi(t,e,n,i){let o=[];i<=-180&&(i=-179.9999),n<=-90&&(n=-89.9999),t>=90&&(t=89.9999),e>=180&&(e=179.9999);let r=Math.min(90,(e-i)/Math.ceil((e-i)/90)),a=(t-n)/Math.ceil((t-n)/90);for(let s=i;s<e;s+=r){let i=[];o.push([i]),s+r>e&&(r=e-s);for(let e=s;e<=s+r;e+=5)i.push([e,t]);for(let e=t;e>=n;e-=a)i.push([s+r,e]);for(let t=s+r;t>=s;t-=5)i.push([t,n]);for(let e=n;e<=t;e+=a)i.push([s,e])}return{type:"MultiPolygon",coordinates:o}}function Ni(t){let e=ji(t.longitude),n=Math.asin(Math.sin(t.latitude*Cn.RADIANS))*Cn.DEGREES,i=ji(t.latitude);return Math.abs(i)>90&&(e=ji(e+180)),t.longitude=e,t.latitude=n,t}function ji(t){return(t%=360)>180&&(t-=360),t<-180&&(t+=360),t}var Ci=n(7652);class Ei extends fe.j{constructor(){super(...arguments),Object.defineProperty(this,"_downTranslateX",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_downTranslateY",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_downRotationX",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_downRotationY",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_downRotationZ",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_pLat",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_pLon",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_movePoints",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"_downZoomLevel",{enumerable:!0,configurable:!0,writable:!0,value:1}),Object.defineProperty(this,"_doubleDownDistance",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_dirtyGeometries",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"_geometryColection",{enumerable:!0,configurable:!0,writable:!0,value:{type:"GeometryCollection",geometries:[]}}),Object.defineProperty(this,"_centerLocation",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"_za",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_rxa",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_rya",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_txa",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tya",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_mapBounds",{enumerable:!0,configurable:!0,writable:!0,value:[[0,0],[0,0]]}),Object.defineProperty(this,"_geoCentroid",{enumerable:!0,configurable:!0,writable:!0,value:{longitude:0,latitude:0}}),Object.defineProperty(this,"_geoBounds",{enumerable:!0,configurable:!0,writable:!0,value:{left:0,right:0,top:0,bottom:0}}),Object.defineProperty(this,"_prevGeoBounds",{enumerable:!0,configurable:!0,writable:!0,value:{left:0,right:0,top:0,bottom:0}}),Object.defineProperty(this,"_dispatchBounds",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"_wheelDp",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_pw",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_ph",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_mapFitted",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"_centerX",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_centerY",{enumerable:!0,configurable:!0,writable:!0,value:0})}_makeGeoPath(){const t=this.get("projection"),e=function(t,e){let n,i,o=3,r=4.5;function a(t){return t&&("function"==typeof r&&i.pointRadius(+r.apply(this,arguments)),Z(t,n(i))),i.result()}return a.area=function(t){return Z(t,n(Me)),Me.result()},a.measure=function(t){return Z(t,n(on)),on.result()},a.bounds=function(t){return Z(t,n(Jt)),Jt.result()},a.centroid=function(t){return Z(t,n(Ve)),Ve.result()},a.projection=function(e){return arguments.length?(n=null==e?(t=null,Yt):(t=e).stream,a):t},a.context=function(t){return arguments.length?(i=null==t?(e=null,new un(o)):new Je(e=t),"function"!=typeof r&&i.pointRadius(r),a):e},a.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(i.pointRadius(+t),+t),a):r},a.digits=function(t){if(!arguments.length)return o;if(null==t)o=null;else{const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);o=e}return null===e&&(i=new un(o)),a},a.projection(t).digits(o).context(e)}();e.projection(t),this.setPrivateRaw("geoPath",e)}geoPoint(){return this.invert(this.seriesContainer.toGlobal({x:this.width()/2,y:this.height()/2}))}geoCentroid(){return this._geoCentroid}geoBounds(){return this._geoBounds}_handleSetWheel(){const t=this.get("wheelX"),e=this.get("wheelY"),n=this.chartContainer;"none"!=t||"none"!=e?(this._wheelDp&&this._wheelDp.dispose(),this._wheelDp=n.events.on("wheel",(i=>{const o=this.get("wheelEasing"),r=this.get("wheelSensitivity",1),a=this.get("wheelDuration",0),s=i.originalEvent;if(!Ci.isLocalEvent(s,this))return;s.preventDefault();const l=n._display.toLocal(i.point);"zoom"==e?this._handleWheelZoom(s.deltaY,l):"rotateY"==e?this._handleWheelRotateY(s.deltaY/5*r,a,o):"rotateX"==e&&this._handleWheelRotateX(s.deltaY/5*r,a,o),"zoom"==t?this._handleWheelZoom(s.deltaX,l):"rotateY"==t?this._handleWheelRotateY(s.deltaX/5*r,a,o):"rotateX"==t&&this._handleWheelRotateX(s.deltaX/5*r,a,o)})),this._disposers.push(this._wheelDp)):this._wheelDp&&this._wheelDp.dispose()}_prepareChildren(){super._prepareChildren();const t=this.get("projection"),e=this.innerWidth(),n=this.innerHeight(),i=this._geometryColection.geometries;if(this.isDirty("projection")){this._makeGeoPath(),this.markDirtyProjection(),this._fitMap(),t.scale(this.getPrivate("mapScale")*this.get("zoomLevel",1)),t.rotate&&t.rotate([this.get("rotationX",0),this.get("rotationY",0),this.get("rotationZ",0)]);let i=this._prevSettings.projection;if(i&&i!=t){let o=e/2,r=n/2;if(i.invert){let e=i.invert([o,r]);if(e){let n=t(e);if(n){let e=t.translate(),i=o-(n[0]-e[0]),a=r-(n[1]-e[1]);t.translate([i,a]),this.setRaw("translateX",i),this.setRaw("translateY",a)}}}}}if((this.isDirty("wheelX")||this.isDirty("wheelY"))&&this._handleSetWheel(),this._dirtyGeometries&&(this._geometryColection.geometries=[],this.series.each((t=>{o.pushAll(this._geometryColection.geometries,t._geometries)})),this._fitMap()),0!=i.length&&(e!=this._pw||n!=this._ph||this._dirtyGeometries)&&e>0&&n>0){let i=e/2,o=n/2;t.fitSize([e,n],this._geometryColection);const r=t.scale();if(this.setPrivateRaw("mapScale",r),t.scale(r*this.get("zoomLevel",1)),this._centerLocation){let e=t(this._centerLocation);if(e){let n=t.translate(),r=i-(e[0]-n[0]),a=o-(e[1]-n[1]);t.translate([r,a]),this.setRaw("translateX",r),this.setRaw("translateY",a),this._centerX=n[0],this._centerY=n[1]}}this.markDirtyProjection();const a=this.getPrivate("geoPath");this._mapBounds=a.bounds(this._geometryColection)}if(this._pw=e,this._ph=n,this.isDirty("zoomControl")){const t=this._prevSettings.zoomControl,e=this.get("zoomControl");e!==t&&(this._disposeProperty("zoomControl"),t&&t.dispose(),e&&(e.setPrivate("chart",this),this.children.push(e)),this.setRaw("zoomControl",e))}this.isDirty("zoomLevel")&&(t.scale(this.getPrivate("mapScale")*this.get("zoomLevel",1)),this.markDirtyProjection(),this.series.each((t=>{t.isType("MapPointSeries")&&t.get("autoScale")&&o.each(t.dataItems,(t=>{const e=t.bullets;e&&o.each(e,(t=>{const e=t.get("sprite");e&&e.set("scale",this.get("zoomLevel"))}))}))}))),(this.isDirty("translateX")||this.isDirty("translateY"))&&(t.translate([this.get("translateX",this.width()/2),this.get("translateY",this.height()/2)]),this.markDirtyProjection()),t.rotate&&(this.isDirty("rotationX")||this.isDirty("rotationY")||this.isDirty("rotationZ"))&&(t.rotate([this.get("rotationX",0),this.get("rotationY",0),this.get("rotationZ",0)]),this.markDirtyProjection()),(this.isDirty("pinchZoom")||this.get("panX")||this.get("panY"))&&this._setUpTouch()}_fitMap(){const t=this.get("projection");let e=this.innerWidth(),n=this.innerHeight();if(e>0&&n>0){t.fitSize([e,n],this._geometryColection),this.setPrivateRaw("mapScale",t.scale());const i=t.translate();this.setRaw("translateX",i[0]),this.setRaw("translateY",i[1]),this._centerX=i[0],this._centerY=i[1];const o=this.getPrivate("geoPath");this._mapBounds=o.bounds(this._geometryColection),this._geoCentroid=xi(this._geometryColection);const r=Si(this._geometryColection);if(this._geoBounds=r,this._geometryColection.geometries.length>0){r.left=Cn.round(this._geoBounds.left,3),r.right=Cn.round(this._geoBounds.right,3),r.top=Cn.round(this._geoBounds.top,3),r.bottom=Cn.round(this._geoBounds.bottom,3);const t=this._prevGeoBounds;t&&!Ci.sameBounds(r,t)&&(this._dispatchBounds=!0,this._prevGeoBounds=r)}this._mapFitted=!0}}homeGeoPoint(){let t=this.get("homeGeoPoint");if(!t){const e=this.getPrivate("geoPath").bounds(this._geometryColection),n=e[0][0],i=e[0][1],o=e[1][0],r=e[1][1];t=this.invert({x:n+(o-n)/2,y:i+(r-i)/2})}return t}goHome(t){this.zoomToGeoPoint(this.homeGeoPoint(),this.get("homeZoomLevel",1),!0,t,this.get("homeRotationX"),this.get("homeRotationY"))}_updateChildren(){const t=this.get("projection");if(t.invert){let e=this.innerWidth(),n=this.innerHeight();e>0&&n>0&&(this._centerLocation=t.invert([this.innerWidth()/2,this.innerHeight()/2]))}super._updateChildren()}_afterChanged(){if(super._afterChanged(),this._dispatchBounds){this._dispatchBounds=!1;const t="geoboundschanged";this.events.isEnabled(t)&&this.events.dispatch(t,{type:t,target:this})}}_setUpTouch(){this.chartContainer._display.cancelTouch||(this.chartContainer._display.cancelTouch=!!(this.get("pinchZoom")||this.get("panX")||this.get("panY")))}markDirtyGeometries(){this._dirtyGeometries=!0,this.markDirty()}markDirtyProjection(){this.series.each((t=>{t.markDirtyProjection()}))}_afterNew(){this._defaultThemes.push(le.new(this._root)),this._settings.themeTags=Ci.mergeTags(this._settings.themeTags,["map"]),this.children.push(this.bulletsContainer),super._afterNew(),this._makeGeoPath(),this.chartContainer.children.push(this.seriesContainer),null==this.get("translateX")&&this.set("translateX",this.width()/2),null==this.get("translateY")&&this.set("translateY",this.height()/2),this.chartContainer.set("interactive",!0),this.chartContainer.set("interactiveChildren",!1),this.chartContainer.set("background",ge.A.new(this._root,{themeTags:["map","background"],fill:Nn.Il.fromHex(0),fillOpacity:0})),this._disposers.push(this.chartContainer.events.on("pointerdown",(t=>{this._handleChartDown(t)}))),this._disposers.push(this.chartContainer.events.on("globalpointerup",(t=>{this._handleChartUp(t)}))),this._disposers.push(this.chartContainer.events.on("globalpointermove",(t=>{this._handleChartMove(t)})));let t=!1;for(let e=0;e<jn.i_.licenses.length;e++)jn.i_.licenses[e].match(/^AM5M.{5,}/i)&&(t=!0);t||this._root._showBranding(),this._setUpTouch()}_handleChartDown(t){this._downZoomLevel=this.get("zoomLevel",1);let e=r.keys(this.chartContainer._downPoints).length;if(1==e){const n=this.chartContainer._downPoints[1];n&&n.x==t.point.x&&n.y==t.point.y&&(e=0)}if(e>0){this._downTranslateX=this.get("translateX"),this._downTranslateY=this.get("translateY"),this._downRotationX=this.get("rotationX"),this._downRotationY=this.get("rotationY"),this._downRotationZ=this.get("rotationZ");const t=this.chartContainer._getDownPointId();if(t){let e=this._movePoints[t];e&&(this.chartContainer._downPoints[t]=e)}}else if(0==e){let e=this.chartContainer.get("background");if(e&&e.events.enableType("click"),this.get("panX")||this.get("panY")){this._za&&this._za.stop(),this._txa&&this._txa.stop(),this._tya&&this._tya.stop(),this._rxa&&this._rxa.stop(),this._rya&&this._rya.stop();const e=this.chartContainer._display.toLocal(t.point);this._downTranslateX=this.get("translateX"),this._downTranslateY=this.get("translateY"),this._downRotationX=this.get("rotationX"),this._downRotationY=this.get("rotationY"),this._downRotationZ=this.get("rotationZ");let n=this.get("projection");if(n.invert){let t=n.invert([e.x,e.y]),i=n.invert([e.x+1,e.y+1]);t&&i&&(this._pLon=Math.abs(i[0]-t[0]),this._pLat=Math.abs(i[1]-t[1]))}}}}invert(t){let e=this.get("projection");if(e.invert){const n=e.invert([t.x,t.y]);if(n)return{longitude:n[0],latitude:n[1]}}return{longitude:0,latitude:0}}convert(t,e,n){let i,o=this.get("projection");if(o.rotate||(e=void 0,n=void 0),null!=e||null!=n){null==e&&(e=0),null==n&&(n=0);let r=o.rotate();o.rotate([e,n,0]),i=o([t.longitude,t.latitude]),o.rotate(r)}else i=o([t.longitude,t.latitude]);return i?{x:i[0],y:i[1]}:{x:0,y:0}}_handleChartUp(t){this.chartContainer._downPoints={}}_handlePinch(){const t=this.chartContainer;let e=0,n=[],i=[];if(r.each(t._downPoints,((t,o)=>{n[e]=o;let r=this._movePoints[t];r&&(i[e]=r),e++})),n.length>1&&i.length>1){const e=t._display;let o=n[0],r=n[1],a=i[0],s=i[1];if(o&&r&&a&&s){o=e.toLocal(o),r=e.toLocal(r),a=e.toLocal(a),s=e.toLocal(s);let t=Math.hypot(r.x-o.x,r.y-o.y),n=Math.hypot(s.x-a.x,s.y-a.y)/t*this._downZoomLevel;n=Cn.fitToRange(n,this.get("minZoomLevel",1),this.get("maxZoomLevel",32));let i={x:a.x+(s.x-a.x)/2,y:a.y+(s.y-a.y)/2},l={x:o.x+(r.x-o.x)/2,y:o.y+(r.y-o.y)/2},u=this._downTranslateX||0,c=this._downTranslateY||0,h=this._downZoomLevel,p=i.x-(-u+l.x)/h*n,f=i.y-(-c+l.y)/h*n;this.set("zoomLevel",n),this.set("translateX",p),this.set("translateY",f)}}}_handleChartMove(t){const e=this.chartContainer;let n=e._getDownPoint();const i=e._getDownPointId(),o=t.originalEvent.pointerId;if(this.get("pinchZoom")&&o&&(this._movePoints[o]=t.point,r.keys(e._downPoints).length>1))this._handlePinch();else if((!i||!o||o==i)&&n){const i=this.get("panX"),o=this.get("panY");if("none"!=i||"none"!=o){const r=e._display;let a=r.toLocal(t.point);n=r.toLocal(n);let l=this._downTranslateX,u=this._downTranslateY;if(Math.hypot(n.x-a.x,n.y-a.y)>5){let t=e.get("background");if(t&&t.events.disableType("click"),s.isNumber(l)&&s.isNumber(u)){let t=this.get("projection");const e=this.get("zoomLevel",1),r=this.get("maxPanOut",.4),s=this._mapBounds,c=this.width(),h=this.height(),p=s[1][0]-s[0][0],f=s[1][1]-s[0][1];if("translateX"==i){l+=a.x-n.x;const t=c/2-(c/2-this._centerX)*e;l=Math.min(l,t+p*r*e),l=Math.max(l,t-p*r*e)}if("translateY"==o){u+=a.y-n.y;const t=h/2-(h/2-this._centerY)*e;u=Math.min(u,t+f*r*e),u=Math.max(u,t-f*r*e)}if(this.set("translateX",l),this.set("translateY",u),t.invert){let e=t.invert([n.x,n.y]);location&&e&&("rotateX"==i&&this.set("rotationX",this._downRotationX-(n.x-a.x)*this._pLon),"rotateY"==o&&this.set("rotationY",this._downRotationY+(n.y-a.y)*this._pLat))}}}}}}_handleWheelRotateY(t,e,n){this._rya=this.animate({key:"rotationY",to:this.get("rotationY",0)-t,duration:e,easing:n})}_handleWheelRotateX(t,e,n){this._rxa=this.animate({key:"rotationX",to:this.get("rotationX",0)-t,duration:e,easing:n})}_handleWheelZoom(t,e){let n=this.get("zoomStep",2),i=this.get("zoomLevel",1),o=i;t>0?o=i/n:t<0&&(o=i*n),o!=i&&this.zoomToPoint(e,o)}zoomToGeoBounds(t,e,n,i){t.right<t.left&&(t.right=180,t.left=-180);const o=this.getPrivate("geoPath").bounds(this._geometryColection);let r=this.convert({longitude:t.left,latitude:t.top},n,i),a=this.convert({longitude:t.right,latitude:t.bottom},n,i);r.y<o[0][1]&&(r.y=o[0][1]),a.y>o[1][1]&&(a.y=o[1][1]);let s=this.get("zoomLevel",1),l=r.x,u=a.x,c=r.y,h=a.y,p=this.seriesContainer,f=.9*Math.min(p.innerWidth()/(u-l)*s,p.innerHeight()/(h-c)*s),g=l+(u-l)/2,d=c+(h-c)/2,m=this.invert({x:g,y:d});return null==n&&null==i||this.rotate(n,i),this.zoomToGeoPoint(m,f,!0,e)}zoomToPoint(t,e,n,i){e&&(e=Cn.fitToRange(e,this.get("minZoomLevel",1),this.get("maxZoomLevel",32))),s.isNumber(i)||(i=this.get("animationDuration",0));const o=this.get("animationEasing"),r=this.get("zoomLevel",1);this.get("centerMapOnZoomOut")&&e==this.get("homeZoomLevel",1)&&(t=this.convert(this.homeGeoPoint()),n=!0);let a=t.x,l=t.y,u=this.get("translateX",0),c=this.get("translateY",0),h=a,p=l;n&&(h=this.width()/2,p=this.height()/2);let f=h-(a-u)/r*e,g=p-(l-c)/r*e;return this._txa=this.animate({key:"translateX",to:f,duration:i,easing:o}),this._tya=this.animate({key:"translateY",to:g,duration:i,easing:o}),this._za=this.animate({key:"zoomLevel",to:e,duration:i,easing:o}),r!=e&&this._root.readerAlert(this._t("Zoom level changed to %1",this._root.locale,s.numberToString(e))),this._za}zoomToGeoPoint(t,e,n,i,o,r){let a=this.convert(t,o,r);if(null==o&&null==r||this.rotate(o,r,i),a)return this.zoomToPoint(a,e,n,i)}rotate(t,e,n){if(this.get("projection").rotate){s.isNumber(n)||(n=this.get("animationDuration",0));const i=this.get("animationEasing");null!=t&&this.animate({key:"rotationX",to:t,duration:n,easing:i}),null!=e&&this.animate({key:"rotationY",to:e,duration:n,easing:i})}}zoomIn(){return this.zoomToPoint({x:this.width()/2,y:this.height()/2},this.get("zoomLevel",1)*this.get("zoomStep",2))}zoomOut(){return this.zoomToPoint({x:this.width()/2,y:this.height()/2},this.get("zoomLevel",1)/this.get("zoomStep",2))}_clearDirty(){super._clearDirty(),this._dirtyGeometries=!1,this._mapFitted=!1}getArea(t){const e=this.getPrivate("geoPath"),n=t.get("geometry");return n?e.area(n):0}}Object.defineProperty(Ei,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapChart"}),Object.defineProperty(Ei,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:fe.j.classNames.concat([Ei.className])});class Ii extends a{constructor(){super(...arguments),Object.defineProperty(this,"_types",{enumerable:!0,configurable:!0,writable:!0,value:["Point","MultiPoint"]}),Object.defineProperty(this,"_lineChangedDp",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_afterNew(){this.fields.push("polygonId","lineId","longitude","latitude","fixed"),super._afterNew()}markDirtyProjection(){this.markDirty()}markDirtyValues(t){super.markDirtyValues(),t&&this._positionBullets(t)}processDataItem(t){super.processDataItem(t);let e=t.get("geometry");if(e){if("Point"==e.type){const n=e.coordinates;n&&(t.set("longitude",n[0]),t.set("latitude",n[1]))}else if("MultiPoint"==e.type){const n=e.coordinates;n&&n[0]&&(t.set("longitude",n[0][0]),t.set("latitude",n[0][1]))}}else e={type:"Point",coordinates:[t.get("longitude",0),t.get("latitude",0)]},t.set("geometry",e);this._addGeometry(e,this)}_makeBullets(t){t.bullets=[],this.bullets.each((e=>{const n=t.get("geometry");if(n)if("Point"==n.type)this._setBulletParent(this._makeBullet(t,e));else if(n.type="MultiPoint"){let i=0;o.each(n.coordinates,(()=>{this._setBulletParent(this._makeBullet(t,e,i)),i++}))}}))}_setBulletParent(t){if(t){const e=t.get("sprite"),n=this.chart;if(e&&n){const t=e.dataItem;t&&(t.get("fixed")?e.parent!=n.bulletsContainer&&n.bulletsContainer.children.moveValue(e):e.parent!=this.bulletsContainer&&this.bulletsContainer.children.moveValue(e))}}}_positionBullet(t){const e=t.get("sprite");if(e){const n=e.dataItem;if(n&&n.get("fixed"))return;const i=n.get("latitude"),o=n.get("longitude"),r=n.get("lineDataItem"),a=n.get("fixed"),l=this.chart;let u;if(r)u=r.get("mapLine");else{const t=n.get("lineId");t&&l&&l.series.each((e=>{if(e.isType("MapLineSeries")){let i=e.getDataItemById(t);i&&(n.set("lineDataItem",i),u=i.get("mapLine"))}}))}this._lineChangedDp&&this._lineChangedDp.dispose(),u&&(this._lineChangedDp=u.events.on("linechanged",(()=>{this._positionBullets(n)})));const c=n.get("polygonDataItem");let h;if(c)h=c.get("mapPolygon");else{const t=n.get("polygonId");t&&l&&l.series.each((e=>{if(e.isType("MapPolygonSeries")){let i=e.getDataItemById(t);i&&(n.set("polygonDataItem",i),h=i.get("mapPolygon"))}}))}const p=n.get("positionOnLine");let f,g;if(h){let t=h.visualCentroid();f=[t.longitude,t.latitude],n.setRaw("longitude",t.longitude),n.setRaw("latitude",t.latitude)}else if(u&&s.isNumber(p)){let e=u.positionToGeoPoint(p);if(f=[e.longitude,e.latitude],n.get("autoRotate",t.get("autoRotate"))&&l){const t=u.positionToGeoPoint(p-.002),e=u.positionToGeoPoint(p+.002),n=l.convert(t),i=l.convert(e);g=Cn.getAngle(n,i)}n.setRaw("longitude",e.longitude),n.setRaw("latitude",e.latitude)}else if(s.isNumber(o)&&s.isNumber(i))f=[o,i];else{const e=n.get("geometry");if(e)if("Point"==e.type)this._positionBulletReal(t,e,e.coordinates,g);else if("MultiPoint"==e.type){let n=t._index||0;f=e.coordinates[n]}}!a&&f&&this._positionBulletReal(t,{type:"Point",coordinates:f},f,g)}}_positionBulletReal(t,e,n,i){const o=t.get("sprite"),r=this.chart;if(r){const a=r.get("projection"),s=r.getPrivate("geoPath"),l=o.dataItem,u=a(n);u&&o.setAll({x:u[0],y:u[1]});let c=!0;s(e)?this.get("clipFront")&&(c=!1):this.get("clipBack")&&(c=!1),o.setPrivate("visible",c),l&&null!=i&&l.get("autoRotate",t.get("autoRotate"))&&o.set("rotation",i+l.get("autoRotateAngle",t.get("autoRotateAngle",0)))}}zoomToDataItem(t,e,n){const i=this.chart;if(i){const o=t.get("longitude",0),r=t.get("latitude",0);return n?i.zoomToGeoPoint({longitude:o,latitude:r},e,!0,void 0,-o,-r):i.zoomToGeoPoint({longitude:o,latitude:r},e,!0)}}disposeDataItem(t){const e=this.chart;e&&e.series.each((e=>{e.isType("MapLineSeries")&&o.each(e.dataItems,(n=>{const i=n.get("pointsToConnect");i&&o.each(i,(r=>{r==t&&(o.remove(i,r),e.markDirtyValues(n))}))}))})),super.disposeDataItem(t)}_excludeDataItem(t){super._excludeDataItem(t);const e=t.bullets;e&&o.each(e,(t=>{const e=t.get("sprite");e&&e.setPrivate("visible",!1)}))}_unexcludeDataItem(t){super._unexcludeDataItem(t);const e=t.bullets;e&&o.each(e,(t=>{const e=t.get("sprite");e&&e.setPrivate("visible",!0)}))}_notIncludeDataItem(t){super._notIncludeDataItem(t);const e=t.bullets;e&&o.each(e,(t=>{const e=t.get("sprite");e&&e.setPrivate("visible",!1)}))}_unNotIncludeDataItem(t){super._unNotIncludeDataItem(t);const e=t.bullets;e&&o.each(e,(t=>{const e=t.get("sprite");e&&e.setPrivate("visible",!0)}))}}Object.defineProperty(Ii,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapPointSeries"}),Object.defineProperty(Ii,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:a.classNames.concat([Ii.className])});var Oi=n(5417),Ti=n.n(Oi);class Li extends u.T{constructor(){super(...arguments),Object.defineProperty(this,"_projectionDirty",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"series",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_beforeChanged(){if(super._beforeChanged(),this._projectionDirty||this.isDirty("geometry")||this.isDirty("precision")){const t=this.get("geometry");if(t){const e=this.series;if(e){const n=e.projection();n&&n.precision(this.get("precision",.5));const i=e.geoPath();i&&(this._clear=!0,this.set("draw",(e=>{i.context(this._display),i(t),i.context(null)})),this.isHover()&&this.showTooltip())}}}}markDirtyProjection(){this.markDirty(),this._projectionDirty=!0}_clearDirty(){super._clearDirty(),this._projectionDirty=!1}geoCentroid(){const t=this.get("geometry");return t?xi(t):{latitude:0,longitude:0}}visualCentroid(){let t=0,e=[];const n=this.get("geometry");if(n){if("Polygon"==n.type)e=n.coordinates;else if("MultiPolygon"==n.type)for(let i=0;i<n.coordinates.length;i++){let o=n.coordinates[i],r=ci({type:"Polygon",coordinates:o});r>t&&(e=o,t=r)}let i=Ti()(e);return{longitude:i[0],latitude:i[1]}}return{longitude:0,latitude:0}}_getTooltipPoint(){const t=this.series;if(t){const e=t.projection();if(e){const t=this.visualCentroid(),n=e([t.longitude,t.latitude]);if(n)return{x:n[0],y:n[1]}}}return{x:0,y:0}}}Object.defineProperty(Li,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapPolygon"}),Object.defineProperty(Li,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:u.T.classNames.concat([Li.className])});class ki extends a{constructor(){super(...arguments),Object.defineProperty(this,"mapPolygons",{enumerable:!0,configurable:!0,writable:!0,value:new K.o(tt.YS.new({}),(()=>Li._new(this._root,{},[this.mapPolygons.template])))}),Object.defineProperty(this,"_types",{enumerable:!0,configurable:!0,writable:!0,value:["Polygon","MultiPolygon"]})}makeMapPolygon(t){const e=this.children.push(this.mapPolygons.make());return e._setDataItem(t),this.mapPolygons.push(e),e}markDirtyProjection(){o.each(this.dataItems,(t=>{let e=t.get("mapPolygon");e&&e.markDirtyProjection()}))}_prepareChildren(){super._prepareChildren(),this.isDirty("fill")&&this.mapPolygons.template.set("fill",this.get("fill")),this.isDirty("stroke")&&this.mapPolygons.template.set("stroke",this.get("stroke"))}processDataItem(t){super.processDataItem(t);let e=t.get("mapPolygon");e||(e=this.makeMapPolygon(t)),t.set("mapPolygon",e);let n=t.get("geometry");if(n){if(this.get("reverseGeodata")&&n.coordinates)for(let t=0;t<n.coordinates.length;t++)if("MultiPolygon"==n.type)for(let e=0;e<n.coordinates[t].length;e++)n.coordinates[t][e].reverse();else n.coordinates[t].reverse();e.set("geometry",n)}e.series=this,this._addGeometry(t.get("geometry"),this)}disposeDataItem(t){super.disposeDataItem(t);const e=t.get("mapPolygon");e&&(this.mapPolygons.removeValue(e),e.dispose()),this._removeGeometry(t.get("geometry"))}_excludeDataItem(t){super._excludeDataItem(t);const e=t.get("mapPolygon");e&&e.setPrivate("visible",!1)}_unexcludeDataItem(t){super._unexcludeDataItem(t);const e=t.get("mapPolygon");e&&e.setPrivate("visible",!0)}_notIncludeDataItem(t){super._notIncludeDataItem(t);const e=t.get("mapPolygon");e&&e.setPrivate("visible",!1)}_unNotIncludeDataItem(t){super._unNotIncludeDataItem(t);const e=t.get("mapPolygon");e&&e.setPrivate("visible",!0)}markDirtyValues(t){if(super.markDirtyValues(),t){const e=t.get("mapPolygon");e&&e.set("geometry",t.get("geometry"))}}zoomToDataItem(t,e){const n=t.get("mapPolygon");if(n){const t=n.get("geometry"),i=this.chart;if(t&&i){if(e){const e=xi(t);return i.rotate(-e.longitude,-e.latitude),i.zoomToGeoBounds(Si(t),void 0,-e.longitude,-e.latitude)}return i.zoomToGeoBounds(Si(t))}}}}Object.defineProperty(ki,"className",{enumerable:!0,configurable:!0,writable:!0,value:"MapPolygonSeries"}),Object.defineProperty(ki,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:a.classNames.concat([ki.className])});var zi=n(8777),Ri=n(8054),Gi=n(7449);class Yi extends zi.W{constructor(){super(...arguments),Object.defineProperty(this,"plusButton",{enumerable:!0,configurable:!0,writable:!0,value:this.children.push(Ri.z.new(this._root,{width:36,height:36,themeTags:["plus"]}))}),Object.defineProperty(this,"minusButton",{enumerable:!0,configurable:!0,writable:!0,value:this.children.push(Ri.z.new(this._root,{width:36,height:36,themeTags:["minus"]}))}),Object.defineProperty(this,"_disposer",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_afterNew(){super._afterNew(),this.set("position","absolute"),this.set("layout",this._root.verticalLayout),this.set("themeTags",["zoomcontrol"]),this.plusButton.setAll({icon:u.T.new(this._root,{themeTags:["icon"]}),layout:void 0}),this.minusButton.setAll({icon:u.T.new(this._root,{themeTags:["icon"]}),layout:void 0})}_prepareChildren(){if(super._prepareChildren(),this.isPrivateDirty("chart")){const t=this.getPrivate("chart"),e=this._prevPrivateSettings.chart;t&&(this._disposer=new Gi.FV([this.plusButton.events.on("click",(()=>{t.zoomIn()})),this.minusButton.events.on("click",(()=>{t.zoomOut()}))])),e&&this._disposer&&this._disposer.dispose()}}}function Bi(t,e){return[P(e)*N(t),N(e)]}function Xi(){return ie(Bi).scale(249.5).clipAngle(90.000001)}function Ai(t,e){return[t,e]}function Zi(){return ie(Ai).scale(152.63)}function Wi(t,e){var n=N(t),i=(n+N(e))/2;if(_(i)<h)return function(t){var e=P(t);function n(t,n){return[t*e,N(n)/e]}return n.invert=function(t,n){return[t/e,I(n*e)]},n}(t);var o=1+n*(2*i-n),r=C(o)/i;function a(t,e){var n=C(o-2*i*N(e))/i;return[n*N(t*=i),r-n*P(t)]}return a.invert=function(t,e){var n=r-e,a=w(t,_(n))*j(n);return n*i<0&&(a-=f*j(t)*j(n)),[a/i,I((o-(t*t+n*n)*i*i)/(2*i))]},a}function Fi(){return function(t){var e=0,n=f/3,i=oe(t),o=i(e,n);return o.parallels=function(t){return arguments.length?i(e=t[0]*v,n=t[1]*v):[e*y,n*y]},o}(Wi).scale(155.424).center([0,33.6442])}function Vi(){var t,e,n,i,o,r,a=Fi().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7]),s=Fi().rotate([154,0]).center([-2,58.5]).parallels([55,65]),l=Fi().rotate([157,0]).center([-3,19.9]).parallels([8,18]),u={point:function(t,e){r=[t,e]}};function c(t){var e=t[0],a=t[1];return r=null,n.point(e,a),r||(i.point(e,a),r)||(o.point(e,a),r)}function p(){return t=e=null,c}return c.invert=function(t){var e=a.scale(),n=a.translate(),i=(t[0]-n[0])/e,o=(t[1]-n[1])/e;return(o>=.12&&o<.234&&i>=-.425&&i<-.214?s:o>=.166&&o<.234&&i>=-.214&&i<-.115?l:a).invert(t)},c.stream=function(n){return t&&e===n?t:(i=[a.stream(e=n),s.stream(n),l.stream(n)],o=i.length,t={point:function(t,e){for(var n=-1;++n<o;)i[n].point(t,e)},sphere:function(){for(var t=-1;++t<o;)i[t].sphere()},lineStart:function(){for(var t=-1;++t<o;)i[t].lineStart()},lineEnd:function(){for(var t=-1;++t<o;)i[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<o;)i[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<o;)i[t].polygonEnd()}});var i,o},c.precision=function(t){return arguments.length?(a.precision(t),s.precision(t),l.precision(t),p()):a.precision()},c.scale=function(t){return arguments.length?(a.scale(t),s.scale(.35*t),l.scale(t),c.translate(a.translate())):a.scale()},c.translate=function(t){if(!arguments.length)return a.translate();var e=a.scale(),r=+t[0],c=+t[1];return n=a.translate(t).clipExtent([[r-.455*e,c-.238*e],[r+.455*e,c+.238*e]]).stream(u),i=s.translate([r-.307*e,c+.201*e]).clipExtent([[r-.425*e+h,c+.12*e+h],[r-.214*e-h,c+.234*e-h]]).stream(u),o=l.translate([r-.205*e,c+.212*e]).clipExtent([[r-.214*e+h,c+.166*e+h],[r-.115*e-h,c+.234*e-h]]).stream(u),p()},c.fitExtent=function(t,e){return Ht(c,t,e)},c.fitSize=function(t,e){return Qt(c,t,e)},c.fitWidth=function(t,e){return Ut(c,t,e)},c.fitHeight=function(t,e){return qt(c,t,e)},c.scale(1070)}Object.defineProperty(Yi,"className",{enumerable:!0,configurable:!0,writable:!0,value:"ZoomControl"}),Object.defineProperty(Yi,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:zi.W.classNames.concat([Yi.className])}),Bi.invert=function(t){return function(e,n){var i=C(e*e+n*n),o=t(i),r=N(o),a=P(o);return[w(e*r,i*a),I(i&&n*r/i)]}}(I),Ai.invert=Ai;var Ji=1.340264,$i=-.081106,Hi=893e-6,Qi=.003796,Ui=C(3)/2;function qi(t,e){var n=I(Ui*N(e)),i=n*n,o=i*i*i;return[t*P(n)/(Ui*(Ji+3*$i*i+o*(7*Hi+9*Qi*i))),n*(Ji+$i*i+o*(Hi+Qi*i))]}function Ki(){return ie(qi).scale(177.158)}function to(t,e){var n=e*e,i=n*n;return[t*(.8707-.131979*n+i*(i*(.003971*n-.001529*i)-.013791)),e*(1.007226+n*(.015085+i*(.028874*n-.044475-.005916*i)))]}function eo(){return ie(to).scale(175.295)}qi.invert=function(t,e){for(var n,i=e,o=i*i,r=o*o*o,a=0;a<12&&(r=(o=(i-=n=(i*(Ji+$i*o+r*(Hi+Qi*o))-e)/(Ji+3*$i*o+r*(7*Hi+9*Qi*o)))*i)*o*o,!(_(n)<p));++a);return[Ui*t*(Ji+3*$i*o+r*(7*Hi+9*Qi*o))/P(i),I(N(i)/Ui)]},to.invert=function(t,e){var n,i=e,o=25;do{var r=i*i,a=r*r;i-=n=(i*(1.007226+r*(.015085+a*(.028874*r-.044475-.005916*a)))-e)/(1.007226+r*(.045255+a*(.259866*r-.311325-.005916*11*a)))}while(_(n)>h&&--o>0);return[t/(.8707+(r=i*i)*(r*(r*r*r*(.003971-.001529*r)-.013791)-.131979)),i]}},5417:function(t,e,n){var i=n(2640);function o(t,e,n){var o,s,l,u;e=e||1;for(var c=0;c<t[0].length;c++){var h=t[0][c];(!c||h[0]<o)&&(o=h[0]),(!c||h[1]<s)&&(s=h[1]),(!c||h[0]>l)&&(l=h[0]),(!c||h[1]>u)&&(u=h[1])}var p=l-o,f=u-s,g=Math.min(p,f),d=g/2;if(0===g){var m=[o,s];return m.distance=0,m}for(var y=new i(void 0,r),v=o;v<l;v+=g)for(var _=s;_<u;_+=g)y.push(new a(v+d,_+d,d,t));var b=function(t){for(var e=0,n=0,i=0,o=t[0],r=0,s=o.length,l=s-1;r<s;l=r++){var u=o[r],c=o[l],h=u[0]*c[1]-c[0]*u[1];n+=(u[0]+c[0])*h,i+=(u[1]+c[1])*h,e+=3*h}return 0===e?new a(o[0][0],o[0][1],0,t):new a(n/e,i/e,0,t)}(t),w=new a(o+p/2,s+f/2,0,t);w.d>b.d&&(b=w);for(var P=y.length;y.length;){var x=y.pop();x.d>b.d&&(b=x,n&&console.log("found best %d after %d probes",Math.round(1e4*x.d)/1e4,P)),x.max-b.d<=e||(d=x.h/2,y.push(new a(x.x-d,x.y-d,d,t)),y.push(new a(x.x+d,x.y-d,d,t)),y.push(new a(x.x-d,x.y+d,d,t)),y.push(new a(x.x+d,x.y+d,d,t)),P+=4)}n&&(console.log("num probes: "+P),console.log("best distance: "+b.d));var D=[b.x,b.y];return D.distance=b.d,D}function r(t,e){return e.max-t.max}function a(t,e,n,i){this.x=t,this.y=e,this.h=n,this.d=function(t,e,n){for(var i=!1,o=1/0,r=0;r<n.length;r++)for(var a=n[r],l=0,u=a.length,c=u-1;l<u;c=l++){var h=a[l],p=a[c];h[1]>e!=p[1]>e&&t<(p[0]-h[0])*(e-h[1])/(p[1]-h[1])+h[0]&&(i=!i),o=Math.min(o,s(t,e,h,p))}return 0===o?0:(i?1:-1)*Math.sqrt(o)}(t,e,i),this.max=this.d+this.h*Math.SQRT2}function s(t,e,n,i){var o=n[0],r=n[1],a=i[0]-o,s=i[1]-r;if(0!==a||0!==s){var l=((t-o)*a+(e-r)*s)/(a*a+s*s);l>1?(o=i[0],r=i[1]):l>0&&(o+=a*l,r+=s*l)}return(a=t-o)*a+(s=e-r)*s}i.default&&(i=i.default),t.exports=o,t.exports.default=o},2640:function(t,e,n){n.r(e),n.d(e,{default:function(){return i}});class i{constructor(t=[],e=o){if(this.data=t,this.length=this.data.length,this.compare=e,this.length>0)for(let t=(this.length>>1)-1;t>=0;t--)this._down(t)}push(t){this.data.push(t),this.length++,this._up(this.length-1)}pop(){if(0===this.length)return;const t=this.data[0],e=this.data.pop();return this.length--,this.length>0&&(this.data[0]=e,this._down(0)),t}peek(){return this.data[0]}_up(t){const{data:e,compare:n}=this,i=e[t];for(;t>0;){const o=t-1>>1,r=e[o];if(n(i,r)>=0)break;e[t]=r,t=o}e[t]=i}_down(t){const{data:e,compare:n}=this,i=this.length>>1,o=e[t];for(;t<i;){let i=1+(t<<1),r=e[i];const a=i+1;if(a<this.length&&n(e[a],r)<0&&(i=a,r=e[a]),n(r,o)>=0)break;e[t]=r,t=i}e[t]=o}}function o(t,e){return t<e?-1:t>e?1:0}},2872:function(t,e,n){n.r(e),n.d(e,{am5map:function(){return i}});const i=n(6970)}},function(t){var e=(2872,t(t.s=2872)),n=window;for(var i in e)n[i]=e[i];e.__esModule&&Object.defineProperty(n,"__esModule",{value:!0})}]);
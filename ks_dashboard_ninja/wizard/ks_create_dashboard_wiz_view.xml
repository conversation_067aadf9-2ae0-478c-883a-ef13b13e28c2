<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ks_create_dashboard_wizard_form" model="ir.ui.view">
        <field name="name">ks.dashboard.wizard.form.view</field>
        <field name="model">ks.dashboard.wizard</field>
        <field name="arch" type="xml">
            <form string="Dashboard Manager">
                <group>
                    <group>
                        <field name="name"/>
                        <field name="ks_template"/>
                    </group>
                    <group>
                        <field name="ks_menu_name"/>
                        <field name="ks_top_menu_id"/>
                        <field name="ks_sequence"/>
                    </group>
                </group>

                <h3 class="oe_grey" invisible="('ks_template','!=',8)or('ks_template','!=',2)or
                ('ks_template','!=',3) or ('ks_template','!=',4) or ('ks_template','!=',5) or ('ks_template','!=',6) or ('ks_template','!=',7)">Preview</h3>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template1.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',2)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template2.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',3)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template3.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',4)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_account_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',5)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_crm_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',6)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_inventory_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',7)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_sale_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',8)"/>

                <footer>
                    <button class="btn-primary" name="ks_create_record" type="object" string="Save"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="ks_create_dashboard_wizard" model="ir.actions.act_window">
        <field name="name">Create Dashboard</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">ks.dashboard.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="ks_create_dashboard_wizard_form"/>
        <field name="target">new</field>
    </record>
</odoo>
